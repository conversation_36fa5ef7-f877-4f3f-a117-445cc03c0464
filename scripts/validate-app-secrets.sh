#!/bin/bash

# Multi-Application Type Secrets Validation Script
# This script helps validate that all required GitHub secrets are configured
# for your specific application type before running the staging deployment.

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_header() {
    echo -e "${BLUE}============================================${NC}"
    echo -e "${BLUE}  Multi-App Deployment Secrets Validator${NC}"
    echo -e "${BLUE}============================================${NC}"
    echo ""
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if GitHub CLI is installed and authenticated
check_gh_cli() {
    if ! command -v gh &> /dev/null; then
        print_error "GitHub CLI (gh) is not installed"
        echo "Please install it from: https://cli.github.com/"
        exit 1
    fi

    if ! gh auth status &> /dev/null; then
        print_error "GitHub CLI is not authenticated"
        echo "Please run: gh auth login"
        exit 1
    fi

    print_success "GitHub CLI is installed and authenticated"
}

# Get repository information
get_repo_info() {
    REPO_OWNER=$(gh repo view --json owner --jq '.owner.login' 2>/dev/null || echo "")
    REPO_NAME=$(gh repo view --json name --jq '.name' 2>/dev/null || echo "")
    
    if [ -z "$REPO_OWNER" ] || [ -z "$REPO_NAME" ]; then
        print_error "Could not determine repository information"
        echo "Please run this script from within a GitHub repository"
        exit 1
    fi

    print_info "Repository: $REPO_OWNER/$REPO_NAME"
}

# Check if a secret exists
check_secret() {
    local secret_name="$1"
    local is_required="$2"
    
    # Use GitHub CLI to check if secret exists (without exposing the value)
    if gh secret list | grep -q "^$secret_name"; then
        print_success "Secret '$secret_name' is configured"
        return 0
    else
        if [ "$is_required" = "true" ]; then
            print_error "Required secret '$secret_name' is missing"
            return 1
        else
            print_warning "Optional secret '$secret_name' is missing"
            return 0
        fi
    fi
}

# Validate secrets for Spring Boot applications
validate_spring_secrets() {
    echo ""
    print_info "Validating Spring Boot application secrets..."
    echo ""
    
    local missing_count=0
    
    # Required secrets
    check_secret "JWT_SECRET_SPRING" "true" || ((missing_count++))
    check_secret "DB_HOST_SPRING_STAGING" "true" || ((missing_count++))
    check_secret "DB_USER_SPRING_STAGING" "true" || ((missing_count++))
    check_secret "DB_PASSWORD_SPRING_STAGING" "true" || ((missing_count++))
    check_secret "DB_NAME_SPRING_STAGING" "true" || ((missing_count++))
    
    # Optional secrets
    check_secret "ENABLE_DATABASE_SPRING" "false"
    check_secret "DB_PORT_SPRING" "false"
    check_secret "DB_SSL_MODE_SPRING" "false"
    check_secret "SMTP_USER_SPRING" "false"
    check_secret "SMTP_PASS_SPRING" "false"
    check_secret "GOOGLE_CLIENT_ID_SPRING" "false"
    check_secret "GOOGLE_CLIENT_SECRET_SPRING" "false"
    
    return $missing_count
}

# Validate secrets for NestJS applications
validate_nest_secrets() {
    echo ""
    print_info "Validating NestJS application secrets..."
    echo ""
    
    local missing_count=0
    
    # Required secrets
    check_secret "JWT_SECRET_NEST" "true" || ((missing_count++))
    check_secret "SESSION_SECRET_NEST" "true" || ((missing_count++))
    check_secret "DB_HOST_NEST_STAGING" "true" || ((missing_count++))
    check_secret "DB_USER_NEST_STAGING" "true" || ((missing_count++))
    check_secret "DB_PASSWORD_NEST_STAGING" "true" || ((missing_count++))
    check_secret "DB_NAME_NEST_STAGING" "true" || ((missing_count++))
    
    # Optional secrets
    check_secret "ENABLE_DATABASE_NEST" "false"
    check_secret "DB_PORT_NEST" "false"
    check_secret "DB_SSL_MODE_NEST" "false"
    check_secret "SMTP_USER_NEST" "false"
    check_secret "SMTP_PASS_NEST" "false"
    check_secret "GOOGLE_CLIENT_ID_NEST" "false"
    check_secret "GOOGLE_CLIENT_SECRET_NEST" "false"
    
    return $missing_count
}

# Validate secrets for Django applications
validate_django_secrets() {
    echo ""
    print_info "Validating Django application secrets..."
    echo ""
    
    local missing_count=0
    
    # Required secrets
    check_secret "JWT_SECRET_DJANGO" "true" || ((missing_count++))
    check_secret "DJANGO_SECRET_KEY" "true" || ((missing_count++))
    check_secret "SESSION_SECRET_DJANGO" "true" || ((missing_count++))
    check_secret "DB_HOST_DJANGO_STAGING" "true" || ((missing_count++))
    check_secret "DB_USER_DJANGO_STAGING" "true" || ((missing_count++))
    check_secret "DB_PASSWORD_DJANGO_STAGING" "true" || ((missing_count++))
    check_secret "DB_NAME_DJANGO_STAGING" "true" || ((missing_count++))
    
    # Optional secrets
    check_secret "ENABLE_DATABASE_DJANGO" "false"
    check_secret "DB_PORT_DJANGO" "false"
    check_secret "DB_SSL_MODE_DJANGO" "false"
    check_secret "SMTP_USER_DJANGO" "false"
    check_secret "SMTP_PASS_DJANGO" "false"
    check_secret "GOOGLE_CLIENT_ID_DJANGO" "false"
    check_secret "GOOGLE_CLIENT_SECRET_DJANGO" "false"
    check_secret "RATE_LIMIT_WINDOW_MS_DJANGO" "false"
    check_secret "RATE_LIMIT_MAX_REQUESTS_DJANGO" "false"
    check_secret "PASSWORD_RESET_TOKEN_EXPIRY_DJANGO" "false"
    check_secret "EMAIL_VERIFICATION_TOKEN_EXPIRY_DJANGO" "false"
    
    return $missing_count
}

# Validate core infrastructure secrets
validate_core_secrets() {
    echo ""
    print_info "Validating core infrastructure secrets..."
    echo ""
    
    local missing_count=0
    
    check_secret "GITOPS_TOKEN" "true" || ((missing_count++))
    check_secret "DIGITALOCEAN_ACCESS_TOKEN" "true" || ((missing_count++))
    
    return $missing_count
}

# Main function
main() {
    print_header
    
    # Check prerequisites
    check_gh_cli
    get_repo_info
    
    # Get application type from user
    echo ""
    print_info "Select your application type:"
    echo "1) Spring Boot (springboot-backend)"
    echo "2) NestJS (nest-backend)"
    echo "3) Django (django-backend)"
    echo "4) Validate all types"
    echo ""
    read -p "Enter your choice (1-4): " choice
    
    local total_missing=0
    
    # Validate core secrets first
    validate_core_secrets
    total_missing=$?
    
    # Validate application-specific secrets
    case $choice in
        1)
            validate_spring_secrets
            total_missing=$((total_missing + $?))
            ;;
        2)
            validate_nest_secrets
            total_missing=$((total_missing + $?))
            ;;
        3)
            validate_django_secrets
            total_missing=$((total_missing + $?))
            ;;
        4)
            validate_spring_secrets
            total_missing=$((total_missing + $?))
            validate_nest_secrets
            total_missing=$((total_missing + $?))
            validate_django_secrets
            total_missing=$((total_missing + $?))
            ;;
        *)
            print_error "Invalid choice. Please run the script again."
            exit 1
            ;;
    esac
    
    # Summary
    echo ""
    echo "============================================"
    if [ $total_missing -eq 0 ]; then
        print_success "All required secrets are configured!"
        print_info "You can now run the staging deployment workflow."
    else
        print_error "$total_missing required secret(s) are missing."
        print_info "Please configure the missing secrets before running the deployment."
        echo ""
        print_info "To add secrets:"
        echo "1. Go to your repository on GitHub"
        echo "2. Navigate to Settings → Secrets and variables → Actions"
        echo "3. Click 'New repository secret'"
        echo "4. Add each missing secret with the exact name shown above"
    fi
    echo "============================================"
}

# Run main function
main "$@"
