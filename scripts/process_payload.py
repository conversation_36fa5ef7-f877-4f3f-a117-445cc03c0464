#!/usr/bin/env python3
"""
GitHub Actions Payload Processing Script
Parses workflow payload and replaces dynamic placeholders in Kustomize manifests
"""

import argparse
import base64
import json
import os
import sys
import shutil
import tempfile
from pathlib import Path
import re


def print_status(message, status_type="INFO"):
    """Print colored status messages"""
    colors = {
        "SUCCESS": "\033[32m[SUCCESS]",
        "ERROR": "\033[31m[ERROR]",
        "WARNING": "\033[33m[WARNING]",
        "INFO": "\033[36m[INFO]"
    }
    reset = "\033[0m"
    color = colors.get(status_type, "")
    print(f"{color} {message}{reset}")


def sanitize_label_value(value):
    """
    Sanitize a string to be a valid Kubernetes label value.

    Kubernetes label values must:
    - Be 63 characters or less
    - Start and end with alphanumeric characters
    - Contain only alphanumeric characters, hyphens, underscores, and dots
    - Not contain forward slashes or other special characters
    """
    if not value:
        return "unknown"

    # Replace invalid characters with hyphens
    # Forward slashes, spaces, and other special chars become hyphens
    sanitized = re.sub(r'[^a-zA-Z0-9._-]', '-', str(value))

    # Remove leading/trailing non-alphanumeric characters
    sanitized = re.sub(r'^[^a-zA-Z0-9]+', '', sanitized)
    sanitized = re.sub(r'[^a-zA-Z0-9]+$', '', sanitized)

    # Ensure it's not empty after sanitization
    if not sanitized:
        sanitized = "unknown"

    # Truncate to 63 characters if needed
    if len(sanitized) > 63:
        sanitized = sanitized[:63]
        # Remove trailing non-alphanumeric after truncation
        sanitized = re.sub(r'[^a-zA-Z0-9]+$', '', sanitized)

    return sanitized


def parse_payload(payload_json):
    """Parse GitHub Actions workflow payload"""
    try:
        if isinstance(payload_json, str):
            payload = json.loads(payload_json)
        else:
            payload = payload_json
        
        print_status("Payload parsed successfully", "SUCCESS")
        return payload
    except json.JSONDecodeError as e:
        print_status(f"Failed to parse payload JSON: {e}", "ERROR")
        return None


def parse_secrets(secrets_encoded):
    """Parse base64-encoded secrets JSON"""
    try:
        if not secrets_encoded or secrets_encoded == "null" or secrets_encoded.strip() == "":
            print_status("No secrets provided", "WARNING")
            return {}

        # Decode base64
        secrets_json = base64.b64decode(secrets_encoded).decode('utf-8')
        secrets = json.loads(secrets_json)

        print_status(f"Parsed {len(secrets)} secrets: {list(secrets.keys())}", "SUCCESS")
        return secrets
    except Exception as e:
        print_status(f"Failed to parse secrets: {e}", "ERROR")
        return {}


def encode_secrets_base64(secrets):
    """Encode secrets to base64 for Kubernetes"""
    encoded_secrets = {}
    for key, value in secrets.items():
        if value:  # Only encode non-empty values
            try:
                # Ensure value is a string and handle special characters
                str_value = str(value).strip()
                if not str_value:
                    print_status(f"Skipping empty secret {key}", "WARNING")
                    continue

                # Encode to base64
                encoded_value = base64.b64encode(str_value.encode('utf-8')).decode('utf-8')

                # Validate the base64 encoding by trying to decode it
                try:
                    base64.b64decode(encoded_value).decode('utf-8')
                    encoded_secrets[f"DYNAMIC_{key}_B64"] = encoded_value
                    print_status(f"Encoded secret {key} -> DYNAMIC_{key}_B64 (length: {len(encoded_value)})", "INFO")
                except Exception as decode_error:
                    print_status(f"Base64 validation failed for {key}: {decode_error}", "ERROR")

            except Exception as e:
                print_status(f"Failed to encode secret {key}: {e}", "ERROR")

    print_status(f"Encoded {len(encoded_secrets)} secrets for base64", "SUCCESS")
    return encoded_secrets


def create_placeholder_mapping(payload, secrets):
    """Create mapping of placeholders to actual values"""
    # Encode secrets to base64
    encoded_secrets = encode_secrets_base64(secrets)
    
    # Create docker image with tag
    docker_image = f"{payload.get('docker_image', 'nginx')}:{payload.get('docker_tag', 'latest')}"
    
    # Sanitize values for Kubernetes label compatibility
    # Kubernetes label values must be valid DNS subdomain names
    # Replace invalid characters with hyphens and ensure they're valid
    source_branch = payload.get('source_branch', 'main')
    sanitized_branch = sanitize_label_value(source_branch)

    source_repo = payload.get('source_repo', 'unknown/repo')
    sanitized_repo = sanitize_label_value(source_repo)

    # Container port is now a required field - must be explicitly provided in payload
    container_port = payload.get('container_port')

    if container_port is None:
        raise ValueError(
            "container_port is a required field in the dispatch payload. "
            "Please specify the container port explicitly (e.g., 3000 for NestJS, 8000 for Django, 8080 for Spring Boot). "
            "This ensures consistent port configuration across all deployments."
        )

    # Convert to string if provided as number
    container_port = str(container_port)

    # Create placeholder mapping
    # Note: Order matters! Longer placeholders must come first to avoid partial replacements
    # Use project_id for both APP_NAME and PROJECT_ID placeholders (app_name field removed from payload)
    project_id = payload.get('project_id', 'unknown-project')
    mapping = {
        'PLACEHOLDER_APP_NAME': project_id,  # Use project_id instead of app_name
        'PLACEHOLDER_PROJECT_ID': project_id,
        'PLACEHOLDER_DOCKER_IMAGE': docker_image,
        'PLACEHOLDER_APPLICATION_TYPE': payload.get('application_type', 'web-app'),
        'PLACEHOLDER_CONTAINER_PORT': container_port,  # Use the determined container port
        'PLACEHOLDER_SOURCE_REPO_LABEL': sanitized_repo,  # Sanitized for labels (must come before PLACEHOLDER_SOURCE_REPO)
        'PLACEHOLDER_SOURCE_BRANCH_LABEL': sanitized_branch,  # Sanitized for labels (must come before PLACEHOLDER_SOURCE_BRANCH)
        'PLACEHOLDER_SOURCE_REPO': source_repo,  # Original for URLs
        'PLACEHOLDER_SOURCE_BRANCH': source_branch,  # Original for display
        'PLACEHOLDER_COMMIT_SHA': payload.get('commit_sha', 'unknown')[:8],  # Short SHA
        # Add APPLICATION_TYPE for conditional processing (without PLACEHOLDER_ prefix)
        'APPLICATION_TYPE': payload.get('application_type', 'web-app'),
    }

    # Add database configuration placeholders from secrets
    if secrets:
        mapping.update({
            'PLACEHOLDER_DB_HOST': secrets.get('DB_HOST', ''),
            'PLACEHOLDER_DB_PORT': secrets.get('DB_PORT', '5432'),
            'PLACEHOLDER_DB_NAME': secrets.get('DB_NAME', ''),
            'PLACEHOLDER_DB_USER': secrets.get('DB_USER', ''),
            'PLACEHOLDER_DB_SSL_MODE': secrets.get('DB_SSL_MODE', 'require'),
        })



    # Generate database URLs if database secrets are available
    if secrets and all(key in secrets for key in ['DB_HOST', 'DB_PORT', 'DB_NAME']):
        # Use DB_SSL_MODE from secrets or default to 'require'
        db_ssl_mode = secrets.get('DB_SSL_MODE', 'require')

        # Generate SPRING_DATASOURCE_URL for Spring Boot applications
        spring_datasource_url = f"jdbc:postgresql://{secrets['DB_HOST']}:{secrets['DB_PORT']}/{secrets['DB_NAME']}?sslmode={db_ssl_mode}"
        # Encode the complete URL to base64
        spring_datasource_url_b64 = base64.b64encode(spring_datasource_url.encode('utf-8')).decode('utf-8')
        encoded_secrets['DYNAMIC_SPRING_DATASOURCE_URL_B64'] = spring_datasource_url_b64
        print_status(f"Generated SPRING_DATASOURCE_URL: {spring_datasource_url}", "INFO")
        print_status(f"Encoded SPRING_DATASOURCE_URL to base64 (length: {len(spring_datasource_url_b64)})", "INFO")

        # Generate DATABASE_URL for Django and NestJS applications (requires user and password)
        if all(key in secrets for key in ['DB_USER', 'DB_PASSWORD']):
            database_url = f"postgresql://{secrets['DB_USER']}:{secrets['DB_PASSWORD']}@{secrets['DB_HOST']}:{secrets['DB_PORT']}/{secrets['DB_NAME']}?sslmode={db_ssl_mode}"
            # Encode the complete URL to base64
            database_url_b64 = base64.b64encode(database_url.encode('utf-8')).decode('utf-8')
            encoded_secrets['DYNAMIC_DATABASE_URL_B64'] = database_url_b64
            print_status(f"Generated DATABASE_URL: {database_url}", "INFO")
            print_status(f"Encoded DATABASE_URL to base64 (length: {len(database_url_b64)})", "INFO")
        else:
            print_status("DB_USER and DB_PASSWORD required for DATABASE_URL generation", "WARNING")

        # Always generate DYNAMIC_DB_SSL_MODE_B64 placeholder (use provided value or default)
        db_ssl_mode_b64 = base64.b64encode(db_ssl_mode.encode('utf-8')).decode('utf-8')
        encoded_secrets['DYNAMIC_DB_SSL_MODE_B64'] = db_ssl_mode_b64
        print_status(f"Generated DB_SSL_MODE: {db_ssl_mode} (default: require)", "INFO")
        print_status(f"Encoded DB_SSL_MODE to base64 (length: {len(db_ssl_mode_b64)})", "INFO")

    # Add encoded secrets to mapping
    mapping.update(encoded_secrets)

    # Debug: Show which DYNAMIC_ placeholders are in the mapping
    dynamic_placeholders = [k for k in mapping.keys() if k.startswith('DYNAMIC_')]
    if dynamic_placeholders:
        print_status(f"Added {len(dynamic_placeholders)} DYNAMIC_ placeholders: {', '.join(dynamic_placeholders)}", "INFO")
    else:
        print_status("No DYNAMIC_ placeholders added to mapping", "WARNING")

    print_status(f"Created mapping with {len(mapping)} placeholders", "SUCCESS")
    return mapping


def process_handlebars_conditionals(content, variables):
    """Process Handlebars-style conditional blocks with improved nested handling"""
    result = content

    # Handle nested conditionals by processing from innermost to outermost
    max_iterations = 20  # Increased to handle more complex nesting
    iteration = 0

    while iteration < max_iterations:
        iteration += 1
        original_result = result

        # Process #eq conditionals first (innermost)
        result = process_eq_conditionals(result, variables)

        # Process #ne conditionals
        result = process_ne_conditionals(result, variables)

        # Process #if conditionals
        result = process_if_conditionals(result, variables)

        # If no changes were made, we're done
        if result == original_result:
            break

    return result


def process_eq_conditionals(content, variables):
    """Process {{#eq VARIABLE 'value'}}...{{else}}...{{/eq}} conditionals with proper nesting support"""
    result = content

    # Process innermost conditionals first by finding the shortest blocks
    max_iterations = 50
    iteration = 0

    while iteration < max_iterations:
        iteration += 1
        original_result = result

        # Find all {{#eq}} patterns
        eq_starts = []
        for match in re.finditer(r'\{\{#eq\s+(\w+)\s+[\'"]([^\'"]*)[\'\"]\}\}', result):
            eq_starts.append((match.start(), match.end(), match.group(1), match.group(2)))

        if not eq_starts:
            break

        # Process the first (innermost) conditional found
        start_pos, end_start_tag, var_name, expected_value = eq_starts[0]

        # Find the matching {{/eq}}
        close_pos = find_matching_block_end(result, end_start_tag, '{{#eq', '{{/eq}}')
        if close_pos == -1:
            break

        # Extract the content between tags
        block_content = result[end_start_tag:close_pos]

        # Check if there's an {{else}} in this block (not in nested blocks)
        else_pos = block_content.find('{{else}}')
        nested_eq_pos = block_content.find('{{#eq')

        # Only consider {{else}} if it's not inside a nested block
        if else_pos != -1 and (nested_eq_pos == -1 or else_pos < nested_eq_pos):
            true_content = block_content[:else_pos]
            false_content = block_content[else_pos + 8:]  # 8 = len('{{else}}')
        else:
            true_content = block_content
            false_content = ""

        # Determine which content to use
        actual_value = str(variables.get(var_name, ""))
        if actual_value == expected_value:
            replacement = true_content
        else:
            replacement = false_content

        # Replace the entire block with the chosen content
        result = result[:start_pos] + replacement + result[close_pos + 7:]  # 7 = len('{{/eq}}')

        # If no changes were made, break to avoid infinite loop
        if result == original_result:
            break

    return result


def process_ne_conditionals(content, variables):
    """Process {{#ne VARIABLE 'value'}}...{{else}}...{{/ne}} conditionals with proper nesting support"""
    result = content

    # Process innermost conditionals first by finding the shortest blocks
    max_iterations = 50
    iteration = 0

    while iteration < max_iterations:
        iteration += 1
        original_result = result

        # Find all {{#ne}} patterns
        ne_starts = []
        for match in re.finditer(r'\{\{#ne\s+(\w+)\s+[\'"]([^\'"]*)[\'\"]\}\}', result):
            ne_starts.append((match.start(), match.end(), match.group(1), match.group(2)))

        if not ne_starts:
            break

        # Process the first (innermost) conditional found
        start_pos, end_start_tag, var_name, expected_value = ne_starts[0]

        # Find the matching {{/ne}}
        close_pos = find_matching_block_end(result, end_start_tag, '{{#ne', '{{/ne}}')
        if close_pos == -1:
            break

        # Extract the content between tags
        block_content = result[end_start_tag:close_pos]

        # Check if there's an {{else}} in this block (not in nested blocks)
        else_pos = block_content.find('{{else}}')
        nested_ne_pos = block_content.find('{{#ne')

        # Only consider {{else}} if it's not inside a nested block
        if else_pos != -1 and (nested_ne_pos == -1 or else_pos < nested_ne_pos):
            true_content = block_content[:else_pos]
            false_content = block_content[else_pos + 8:]  # 8 = len('{{else}}')
        else:
            true_content = block_content
            false_content = ""

        # Determine which content to use
        actual_value = str(variables.get(var_name, ""))
        if actual_value != expected_value:
            replacement = true_content
        else:
            replacement = false_content

        # Replace the entire block with the chosen content
        result = result[:start_pos] + replacement + result[close_pos + 7:]  # 7 = len('{{/ne}}')

        # If no changes were made, break to avoid infinite loop
        if result == original_result:
            break

    return result


def process_if_conditionals(content, variables):
    """Process {{#if VARIABLE}}...{{else}}...{{/if}} conditionals with improved handling"""
    result = content

    # Pattern to match {{#if VARIABLE}}...{{else}}...{{/if}}
    if_pattern = r'\{\{#if\s+(\w+)\}\}(.*?)\{\{else\}\}(.*?)\{\{/if\}\}'

    def replace_if_conditional(match):
        var_name = match.group(1)
        true_content = match.group(2)
        false_content = match.group(3)

        var_value = variables.get(var_name, "")

        # Check if variable is truthy
        if is_truthy(var_value):
            return true_content.strip()
        else:
            return false_content.strip()

    # Process from innermost to outermost by finding the shortest matches first
    while re.search(if_pattern, result, flags=re.DOTALL):
        result = re.sub(if_pattern, replace_if_conditional, result, flags=re.DOTALL, count=1)

    # Handle {{#if VARIABLE}}...{{/if}} without else
    if_pattern_no_else = r'\{\{#if\s+(\w+)\}\}(.*?)\{\{/if\}\}'

    def replace_if_conditional_no_else(match):
        var_name = match.group(1)
        content_block = match.group(2)

        var_value = variables.get(var_name, "")

        # Check if variable is truthy
        if is_truthy(var_value):
            return content_block.strip()
        else:
            return ""

    # Process from innermost to outermost
    while re.search(if_pattern_no_else, result, flags=re.DOTALL):
        result = re.sub(if_pattern_no_else, replace_if_conditional_no_else, result, flags=re.DOTALL, count=1)

    return result


def find_matching_block_end(content, start_pos, open_tag, close_tag):
    """Find the matching closing tag for a conditional block"""
    depth = 1
    pos = start_pos

    while pos < len(content) and depth > 0:
        # Look for the next occurrence of either open or close tag
        next_open = content.find(open_tag, pos)
        next_close = content.find(close_tag, pos)

        # If no more close tags, return -1
        if next_close == -1:
            return -1

        # If there's an open tag before the close tag, increase depth
        if next_open != -1 and next_open < next_close:
            depth += 1
            pos = next_open + len(open_tag)
        else:
            depth -= 1
            if depth == 0:
                return next_close
            pos = next_close + len(close_tag)

    return -1


def is_truthy(value):
    """Check if a value is truthy for template conditionals"""
    if isinstance(value, bool):
        return value
    if isinstance(value, str):
        return value.lower() in ['true', '1', 'yes', 'on'] and value.strip() != ''
    if isinstance(value, (int, float)):
        return value != 0
    return bool(value)


def replace_placeholders_in_file(file_path, mapping):
    """Replace placeholders in a single file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        original_content = content
        replacements_made = 0
        dynamic_replacements = 0

        # First, process Handlebars-style conditionals
        content = process_handlebars_conditionals(content, mapping)

        # Define placeholders that should NOT be replaced as general string replacements
        # These are used only for handlebars conditionals and should not replace YAML keys
        conditional_only_placeholders = {
            'APPLICATION_TYPE',  # Used for {{#eq APPLICATION_TYPE "value"}} conditionals
        }

        # Then replace each placeholder
        for placeholder, value in mapping.items():
            if placeholder in content:
                # Skip placeholders that are meant only for handlebars conditionals
                if placeholder in conditional_only_placeholders:
                    continue

                # Special handling for COMMIT_SHA to ensure it's always quoted as a string in YAML
                if placeholder == 'PLACEHOLDER_COMMIT_SHA':
                    # Ensure the commit SHA is quoted to prevent YAML parsing as number
                    replacement_value = f'"{str(value)}"'
                else:
                    replacement_value = str(value)

                content = content.replace(placeholder, replacement_value)
                replacements_made += 1
                if placeholder.startswith('DYNAMIC_'):
                    dynamic_replacements += 1
                    print_status(f"Replaced {placeholder} in {file_path}", "INFO")

        # Only write if changes were made
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print_status(f"Updated {file_path} ({replacements_made} replacements, {dynamic_replacements} dynamic)", "SUCCESS")
            return True
        else:
            # Check if file contains DYNAMIC_ placeholders that weren't replaced
            if 'DYNAMIC_' in content:
                print_status(f"Warning: {file_path} still contains unreplaced DYNAMIC_ placeholders", "WARNING")
            return False

    except Exception as e:
        print_status(f"Failed to process {file_path}: {e}", "ERROR")
        return False


def process_manifest_directory(manifest_dir, mapping, environment=None, is_template=False):
    """
    Process manifest files with selective environment-based processing.

    For both template and project directories:
    - Process argocd/, components/ directories
    - Process ONLY the target environment's overlay directory (base manifests are now in overlays)
    - EXCLUDE other environment overlay directories to prevent cross-contamination
    """
    manifest_path = Path(manifest_dir)
    if not manifest_path.exists():
        print_status(f"Manifest directory not found: {manifest_dir}", "ERROR")
        return False

    if not environment:
        print_status("Error: Environment must be specified for GitOps deployments to prevent cross-contamination", "ERROR")
        return False

    # File patterns to process
    patterns = ['*.yaml', '*.yml']
    files_processed = 0
    files_updated = 0

    processing_type = "template" if is_template else "project"
    print_status(f"Processing {processing_type} directory with selective environment-based processing", "INFO")
    print_status(f"Target environment: {environment} (will exclude other environment overlays)", "INFO")

    # Note: Base manifests are now integrated into environment-specific overlays
    # No separate base directory processing needed

    # Process ONLY the target environment-specific overlay (exclude other environments)
    overlay_dir = manifest_path / 'overlays' / environment
    if overlay_dir.exists():
        print_status(f"Processing overlay for target environment: {environment}", "INFO")
        for pattern in patterns:
            for file_path in overlay_dir.glob(pattern):
                files_processed += 1
                if replace_placeholders_in_file(file_path, mapping):
                    files_updated += 1

        # Log which environment overlays are being excluded
        overlays_base_dir = manifest_path / 'overlays'
        if overlays_base_dir.exists():
            all_envs = [d.name for d in overlays_base_dir.iterdir() if d.is_dir()]
            excluded_envs = [env for env in all_envs if env != environment]
            if excluded_envs:
                print_status(f"Excluding environment overlays: {', '.join(excluded_envs)}", "INFO")
    else:
        print_status(f"Warning: Overlay directory not found for environment: {environment}", "WARNING")

    # Process ArgoCD manifests (for both template and project directories)
    argocd_dir = manifest_path / 'argocd'
    if argocd_dir.exists():
        print_status("Processing ArgoCD manifests", "INFO")
        for pattern in patterns:
            for file_path in argocd_dir.glob(pattern):
                files_processed += 1
                if replace_placeholders_in_file(file_path, mapping):
                    files_updated += 1
    else:
        print_status("No ArgoCD directory found", "INFO")

    # Process components (for both template and project directories)
    components_dir = manifest_path / 'components'
    if components_dir.exists():
        print_status("Processing component manifests", "INFO")
        for component_dir in components_dir.iterdir():
            if component_dir.is_dir():
                for pattern in patterns:
                    for file_path in component_dir.glob(pattern):
                        files_processed += 1
                        if replace_placeholders_in_file(file_path, mapping):
                            files_updated += 1
    else:
        print_status("No components directory found", "INFO")

    print_status(f"Processed {files_processed} files, updated {files_updated} files", "SUCCESS")
    return files_updated > 0


def filter_components_by_type(project_path, application_type):
    """Filter components based on application type"""
    components_path = project_path / "components"

    if not components_path.exists():
        return

    # Define which components are needed for each application type
    # Note: database-init component is no longer included as a dependency since
    # database initialization is now handled at the environment level via init-container-patch.yaml
    component_mapping = {
        "springboot-backend": [
            "common-labels",
            "resource-limits"
        ],
        "react-frontend": [
            "common-labels",
            "react-frontend",
            "resource-limits"
        ],
        "django-backend": [
            "common-labels",
            "resource-limits"
        ],
        "nodejs-backend": [
            "common-labels",
            "resource-limits"
        ],
        "nest-backend": [
            "common-labels",
            "resource-limits"
        ]
    }

    # Get required components for this application type
    required_components = component_mapping.get(application_type, ["common-labels"])

    # Remove unnecessary components
    for component_dir in components_path.iterdir():
        if component_dir.is_dir() and component_dir.name not in required_components:
            print_status(f"Removing unnecessary component: {component_dir.name}", "INFO")
            shutil.rmtree(component_dir)

def handle_argocd_application_files(manifest_dir, environment):
    """Handle environment-specific ArgoCD application files in the overlays directory"""
    overlays_dir = Path(manifest_dir) / 'overlays'
    if not overlays_dir.exists():
        print_status(f"Missing 'overlays' directory at {overlays_dir}", "ERROR")
        return

    env_dir = overlays_dir / environment
    if not env_dir.exists():
        print_status(f"Environment directory '{environment}' does not exist under overlays.", "WARNING")
        return

    # Source: argocd-application-{env}.yaml
    env_app_file = env_dir / f"argocd-application-{environment}.yaml"
    # Target: application.yaml
    target_app_file = env_dir / "application.yaml"

    if env_app_file.exists():
        if target_app_file.exists():
            target_app_file.unlink()
        env_app_file.rename(target_app_file)
        print_status(f"Renamed {env_app_file.name} to application.yaml in overlays/{environment}", "INFO")

    # Remove other argocd-application-*.yaml files in the same env directory
    for app_file in env_dir.glob("argocd-application-*.yaml"):
        if app_file != env_app_file:
            app_file.unlink()
            print_status(f"Removed unused file: {app_file.name} from overlays/{environment}", "INFO")



def create_processed_manifests(source_dir, output_dir, mapping, environment, project_id=None, application_type=None):
    """Create processed manifests in project-specific directory structure"""
    source_path = Path(source_dir)
    output_path = Path(output_dir)

    # If project_id is provided, create project-specific folder structure
    if project_id:
        project_output_path = output_path / project_id
        project_output_path.mkdir(parents=True, exist_ok=True)

        # Copy manifests to project directory (without 'manifests' subfolder)
        if source_path.exists():
            shutil.copytree(source_path, project_output_path, dirs_exist_ok=True)

            # Filter components based on application type
            if application_type:
                filter_components_by_type(project_output_path, application_type)

            # Handle environment-specific ArgoCD application files
            handle_argocd_application_files(project_output_path, environment)

            # Process the copied manifests (this is a project-specific copy, not a template)
            if process_manifest_directory(project_output_path, mapping, environment, is_template=False):
                # Fix any remaining path-based patches after processing
                fix_kustomization_patches(project_output_path, environment)
                print_status(f"Created project manifests in {project_output_path}", "SUCCESS")
                return project_output_path
            else:
                print_status("No placeholders were replaced", "WARNING")
                # Still fix patches even if no placeholders were replaced
                fix_kustomization_patches(project_output_path, environment)
                return project_output_path
        else:
            print_status(f"Source directory not found: {source_dir}", "ERROR")
            return None
    else:
        # Legacy behavior - create manifests subfolder
        output_path.mkdir(parents=True, exist_ok=True)

        # Copy manifests to output directory
        if source_path.exists():
            shutil.copytree(source_path, output_path / 'manifests', dirs_exist_ok=True)

            # Process the copied manifests (this is a project-specific copy, not a template)
            if process_manifest_directory(output_path / 'manifests', mapping, environment, is_template=False):
                print_status(f"Created processed manifests in {output_path}", "SUCCESS")
                return output_path / 'manifests'
            else:
                print_status("No placeholders were replaced", "WARNING")
                return output_path / 'manifests'
        else:
            print_status(f"Source directory not found: {source_dir}", "ERROR")
            return None


def fix_kustomization_patches(manifest_dir, environment):
    """Fix kustomization files to use inline patches instead of file references"""
    try:
        # Try to import yaml, but continue without it if not available
        yaml_available = True
        try:
            import yaml
        except ImportError:
            yaml_available = False
            print_status("PyYAML not available - using basic text processing for kustomization fixes", "INFO")

        overlay_path = Path(manifest_dir) / 'overlays' / environment
        kustomization_file = overlay_path / 'kustomization.yaml'

        if not kustomization_file.exists():
            print_status(f"No kustomization.yaml found at {kustomization_file} - skipping patch fixes", "INFO")
            return True

        if not yaml_available:
            # Basic validation without YAML parsing
            try:
                with open(kustomization_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                if 'patches:' in content and '../../components/' in content:
                    print_status("Detected component patches in kustomization.yaml - manual review may be needed", "WARNING")
                print_status("Kustomization file exists and appears valid (basic check)", "INFO")
                return True
            except Exception as e:
                print_status(f"Error reading kustomization file: {e}", "WARNING")
                return True

        # Full YAML processing when PyYAML is available
        try:
            # Read kustomization file
            with open(kustomization_file, 'r', encoding='utf-8') as f:
                kustomization = yaml.safe_load(f)

            if not isinstance(kustomization, dict):
                print_status("Invalid kustomization.yaml format", "WARNING")
                return True

            # Check if there are patches with file paths that need to be inlined
            if 'patches' in kustomization and isinstance(kustomization['patches'], list):
                patches_to_fix = []
                for i, patch in enumerate(kustomization['patches']):
                    if isinstance(patch, dict) and 'path' in patch:
                        patch_path = patch['path']
                        # Check if it's a relative path to components
                        if patch_path.startswith('../../components/'):
                            patch_file = overlay_path / patch_path
                            if patch_file.exists():
                                try:
                                    # Read the patch file content
                                    with open(patch_file, 'r', encoding='utf-8') as pf:
                                        patch_content = yaml.safe_load(pf)

                                    # Convert to inline patch
                                    inline_patch = {
                                        'target': patch.get('target', {}),
                                        'patch': yaml.dump(patch_content, default_flow_style=False)
                                    }
                                    patches_to_fix.append((i, inline_patch))
                                    print_status(f"Prepared inline patch for: {patch.get('target', {}).get('name', 'unknown')}", "INFO")
                                except Exception as e:
                                    print_status(f"Failed to process patch file {patch_file}: {e}", "WARNING")

                # Apply fixes
                for i, inline_patch in reversed(patches_to_fix):
                    kustomization['patches'][i] = inline_patch

                if patches_to_fix:
                    # Write back the fixed kustomization
                    with open(kustomization_file, 'w', encoding='utf-8') as f:
                        yaml.dump(kustomization, f, default_flow_style=False, sort_keys=False)
                    print_status(f"Fixed {len(patches_to_fix)} patch references in kustomization.yaml", "SUCCESS")

            return True

        except yaml.YAMLError as e:
            print_status(f"YAML parsing error in kustomization file: {e}", "WARNING")
            return True
        except Exception as e:
            print_status(f"Error processing kustomization file: {e}", "WARNING")
            return True

    except Exception as e:
        print_status(f"Unexpected error in fix_kustomization_patches: {e}", "WARNING")
        return True  # Don't fail the whole process


def check_kubectl_available():
    """Check if kubectl is available"""
    try:
        import subprocess
        subprocess.run(['kubectl', 'version', '--client'],
                      capture_output=True, text=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False


def validate_processed_manifests(manifest_dir, environment):
    """Validate processed manifests using kubectl (non-blocking)"""
    print_status("Starting manifest validation (non-blocking)", "INFO")

    try:
        import subprocess

        # Check if kubectl is available
        if not check_kubectl_available():
            print_status("kubectl not available - skipping validation (this is OK)", "INFO")
            return True

        overlay_path = Path(manifest_dir) / 'overlays' / environment
        validation_path = overlay_path

        # Determine what to validate
        if not overlay_path.exists():
            print_status(f"Environment overlay not found: {overlay_path}", "INFO")
            print_status("No manifests found to validate - skipping", "INFO")
            return True

        # Check for kustomization.yaml
        kustomization_file = validation_path / "kustomization.yaml"
        if not kustomization_file.exists():
            print_status(f"No kustomization.yaml in {validation_path} - checking for individual YAML files", "INFO")

            # Look for individual YAML files to validate
            yaml_files = list(validation_path.glob("*.yaml")) + list(validation_path.glob("*.yml"))
            if yaml_files:
                print_status(f"Found {len(yaml_files)} YAML files for basic validation", "INFO")
                return validate_individual_yaml_files(yaml_files)
            else:
                print_status("No YAML files found to validate", "INFO")
                return True

        # Try to fix kustomization patches (non-blocking)
        try:
            fix_kustomization_patches(manifest_dir, environment)
        except Exception as e:
            print_status(f"Patch fixing failed (continuing anyway): {e}", "INFO")

        print_status(f"Attempting kustomize validation on: {validation_path}", "INFO")

        # Try kustomize build (non-blocking)
        try:
            result = subprocess.run(
                ['kubectl', 'kustomize', str(validation_path)],
                capture_output=True, text=True, check=True, timeout=30
            )
            print_status("Kustomize build successful", "SUCCESS")

            # Try dry-run validation (non-blocking)
            try:
                subprocess.run(
                    ['kubectl', 'apply', '--dry-run=client', '-f', '-'],
                    input=result.stdout, text=True, check=True, timeout=30
                )
                print_status("Manifest validation passed completely", "SUCCESS")
            except subprocess.CalledProcessError as e:
                print_status("Dry-run validation had issues (this is often OK):", "INFO")
                if e.stderr:
                    # Only show first few lines of error to avoid spam
                    error_lines = e.stderr.strip().split('\n')[:3]
                    for line in error_lines:
                        if line.strip():
                            print_status(f"  {line}", "INFO")
                print_status("Continuing with deployment despite validation warnings", "INFO")
            except subprocess.TimeoutExpired:
                print_status("Validation timeout - continuing anyway", "INFO")

        except subprocess.CalledProcessError as e:
            print_status("Kustomize build had issues (this may be OK):", "INFO")
            if e.stderr:
                # Only show first few lines of error
                error_lines = e.stderr.strip().split('\n')[:3]
                for line in error_lines:
                    if line.strip():
                        print_status(f"  {line}", "INFO")
            print_status("Continuing with deployment", "INFO")
        except subprocess.TimeoutExpired:
            print_status("Kustomize build timeout - continuing anyway", "INFO")

        return True

    except Exception as e:
        print_status(f"Validation error (non-blocking): {e}", "INFO")
        print_status("Continuing with deployment", "INFO")
        return True


def validate_individual_yaml_files(yaml_files):
    """Basic validation of individual YAML files"""
    try:
        import subprocess

        valid_files = 0
        for yaml_file in yaml_files:
            try:
                # Basic YAML syntax check
                result = subprocess.run(
                    ['kubectl', 'apply', '--dry-run=client', '-f', str(yaml_file)],
                    capture_output=True, text=True, check=True, timeout=10
                )
                valid_files += 1
            except (subprocess.CalledProcessError, subprocess.TimeoutExpired):
                # Don't fail on individual file issues
                pass

        print_status(f"Basic validation: {valid_files}/{len(yaml_files)} files passed", "INFO")
        return True

    except Exception as e:
        print_status(f"Individual file validation error: {e}", "INFO")
        return True


def main():
    parser = argparse.ArgumentParser(description="Process GitHub Actions payload and replace Kustomize placeholders")
    payload_group = parser.add_mutually_exclusive_group(required=True)
    payload_group.add_argument("--payload", help="GitHub Actions payload JSON string")
    payload_group.add_argument("--payload-file", help="Path to file containing GitHub Actions payload JSON")
    parser.add_argument("--secrets", help="Base64-encoded secrets JSON")
    parser.add_argument("--environment", required=True,
                       choices=["dev", "staging", "production"], help="Target environment")
    parser.add_argument("--manifest-dir", default="manifests", help="Source manifest directory")
    parser.add_argument("--output-dir", default="processed-manifests", help="Output directory")
    parser.add_argument("--validate", action="store_true", help="Validate processed manifests (non-blocking)")
    parser.add_argument("--skip-validation", action="store_true", help="Skip all validation completely")
    parser.add_argument("--in-place", action="store_true", help="Process manifests in place")

    args = parser.parse_args()

    print("GitHub Actions Payload Processing")
    print("=" * 40)

    try:
        # Parse payload from string or file
        if args.payload:
            payload_data = args.payload
        elif args.payload_file:
            try:
                with open(args.payload_file, 'r', encoding='utf-8') as f:
                    payload_data = f.read()
                print_status(f"Read payload from file: {args.payload_file}", "INFO")
            except Exception as e:
                print_status(f"Failed to read payload file {args.payload_file}: {e}", "ERROR")
                sys.exit(1)
        else:
            print_status("No payload provided", "ERROR")
            sys.exit(1)

        payload = parse_payload(payload_data)
        if not payload:
            print_status("Failed to parse payload - exiting", "ERROR")
            sys.exit(1)
    except Exception as e:
        print_status(f"Unexpected error parsing payload: {e}", "ERROR")
        sys.exit(1)
    
    try:
        # Parse secrets - first try from args.secrets, then from payload.secrets_encoded
        secrets = {}
        if args.secrets:
            secrets = parse_secrets(args.secrets)
        elif payload.get('secrets_encoded'):
            print_status("Extracting secrets from payload.secrets_encoded", "INFO")
            secrets = parse_secrets(payload.get('secrets_encoded'))
        else:
            print_status("No secrets provided in args or payload", "WARNING")

        # Create placeholder mapping
        mapping = create_placeholder_mapping(payload, secrets)
    except Exception as e:
        print_status(f"Error processing secrets or creating mapping: {e}", "ERROR")
        sys.exit(1)
    
    try:
        # Process manifests
        if args.in_place:
            # Process manifests in place (this is typically used for template directories)
            print_status("Processing manifests in place...", "INFO")
            # Determine if this is a template directory by checking if it's named 'manifests'
            is_template = Path(args.manifest_dir).name == 'manifests'
            success = process_manifest_directory(args.manifest_dir, mapping, args.environment, is_template=is_template)
            processed_dir = Path(args.manifest_dir)
        else:
            # Create processed manifests in output directory
            print_status("Creating processed manifests...", "INFO")
            project_id = payload.get('project_id')
            application_type = payload.get('application_type')
            processed_dir = create_processed_manifests(
                args.manifest_dir, args.output_dir, mapping, args.environment, project_id, application_type
            )
            success = processed_dir is not None

        if not success:
            print_status("Failed to process manifests", "ERROR")
            sys.exit(1)
    except Exception as e:
        print_status(f"Unexpected error processing manifests: {e}", "ERROR")
        import traceback
        print_status(f"Traceback: {traceback.format_exc()}", "ERROR")
        sys.exit(1)
    
    # Validate if requested and not explicitly skipped
    if args.validate and not args.skip_validation:
        print_status("Running manifest validation (non-blocking)...", "INFO")
        validation_result = validate_processed_manifests(processed_dir, args.environment)
        if not validation_result:
            print_status("Validation had issues - but continuing with deployment", "INFO")
        else:
            print_status("Validation completed successfully", "SUCCESS")
    elif args.skip_validation:
        print_status("Validation skipped as requested", "INFO")
    else:
        print_status("Validation not requested", "INFO")
    
    # Success summary
    print("\n" + "=" * 40)
    print_status("Payload processing completed successfully!", "SUCCESS")
    print(f"\nSummary:")
    print(f"  App Name: {payload.get('app_name')}")
    print(f"  Project ID: {payload.get('project_id')}")
    print(f"  Environment: {args.environment}")
    print(f"  Application Type: {payload.get('application_type')}")
    print(f"  Docker Image: {payload.get('docker_image')}:{payload.get('docker_tag')}")
    print(f"  Source: {payload.get('source_repo')}@{payload.get('source_branch')}")
    print(f"  Commit: {payload.get('commit_sha', 'unknown')[:8]}")
    print(f"  Secrets: {len(secrets)} provided")
    print(f"  Processed Directory: {processed_dir}")
    
    if not args.in_place:
        print(f"\nNext Steps:")
        print(f"  kubectl apply -k {processed_dir}/overlays/{args.environment}")


if __name__ == "__main__":
    main()
