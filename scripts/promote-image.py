#!/usr/bin/env python3
"""
GitOps Image Promotion Script
Promotes Docker images between environments following GitOps principles
"""

import argparse
import json
import os
import sys
import yaml
import subprocess
import requests
from pathlib import Path
from typing import Dict, Optional, <PERSON><PERSON>


def print_status(message: str, status_type: str = "INFO"):
    """Print colored status messages"""
    colors = {
        "SUCCESS": "\033[32m[SUCCESS]",
        "ERROR": "\033[31m[ERROR]",
        "WARNING": "\033[33m[WARNING]",
        "INFO": "\033[36m[INFO]"
    }
    reset = "\033[0m"
    color = colors.get(status_type, "")
    print(f"{color} {message}{reset}")


def extract_image_from_patch(patch_file_path: Path) -> Optional[str]:
    """Extract Docker image from Kustomize patch file"""
    try:
        with open(patch_file_path, 'r') as f:
            patch_data = yaml.safe_load(f)
        
        containers = patch_data.get('spec', {}).get('template', {}).get('spec', {}).get('containers', [])
        if containers and len(containers) > 0:
            image = containers[0].get('image')
            if image and image != 'PLACEHOLDER_DOCKER_IMAGE':
                return image
        
        return None
    except Exception as e:
        print_status(f"Error reading patch file {patch_file_path}: {e}", "ERROR")
        return None


def extract_app_metadata_from_application(app_file_path: Path) -> Dict[str, str]:
    """Extract application metadata from ArgoCD application file"""
    try:
        with open(app_file_path, 'r') as f:
            app_data = yaml.safe_load(f)
        
        metadata = app_data.get('metadata', {})
        labels = metadata.get('labels', {})
        
        return {
            'app_name': metadata.get('name', '').replace('-dev', '').replace('-staging', '').replace('-production', ''),
            'project_id': labels.get('app.kubernetes.io/part-of', ''),
            'application_type': labels.get('app-type', 'web-app'),
            'source_repo': labels.get('source.repo', ''),
            'source_branch': labels.get('source.branch', 'main'),
            'commit_sha': labels.get('app.kubernetes.io/version', 'unknown')
        }
    except Exception as e:
        print_status(f"Error reading application file {app_file_path}: {e}", "ERROR")
        return {}


def get_current_deployment_info(project_id: str, source_env: str) -> Tuple[Optional[str], Dict[str, str]]:
    """Get current deployment information from source environment"""
    print_status(f"Extracting deployment info from {source_env} environment...", "INFO")
    
    # Paths for source environment
    patch_file = Path(f"deployments/{project_id}/overlays/{source_env}/patch-image.yaml")
    app_file = Path(f"deployments/{project_id}/overlays/{source_env}/application.yaml")
    
    # Check if files exist
    if not patch_file.exists():
        print_status(f"Patch file not found: {patch_file}", "ERROR")
        return None, {}
    
    if not app_file.exists():
        print_status(f"Application file not found: {app_file}", "ERROR")
        return None, {}
    
    # Extract image and metadata
    current_image = extract_image_from_patch(patch_file)
    app_metadata = extract_app_metadata_from_application(app_file)
    
    if not current_image:
        print_status(f"Could not extract image from {patch_file}", "ERROR")
        return None, {}
    
    print_status(f"Current {source_env} image: {current_image}", "SUCCESS")
    return current_image, app_metadata


def create_promotion_payload(image: str, app_metadata: Dict[str, str], target_env: str) -> Dict[str, str]:
    """Create GitOps deployment payload for promotion"""
    # Split image into name and tag
    if ':' in image:
        docker_image, docker_tag = image.rsplit(':', 1)
    else:
        docker_image = image
        docker_tag = 'latest'
    
    payload = {
        'project_id': app_metadata.get('project_id', 'unknown-project'),
        'application_type': app_metadata.get('application_type', 'web-app'),
        'environment': target_env,
        'docker_image': docker_image,
        'docker_tag': docker_tag,
        'container_port': app_metadata.get('container_port', 8080),
        'source_repo': app_metadata.get('source_repo', 'unknown/repo'),
        'source_branch': app_metadata.get('source_branch', 'main'),
        'commit_sha': app_metadata.get('commit_sha', 'unknown')
    }
    
    return payload


def trigger_gitops_deployment(payload: Dict[str, str], github_token: str, dry_run: bool = False) -> bool:
    """Trigger GitOps deployment via GitHub repository dispatch"""
    if dry_run:
        print_status("DRY RUN: Would trigger GitOps deployment with payload:", "INFO")
        print(json.dumps(payload, indent=2))
        return True
    
    if not github_token:
        print_status("GitHub token not provided. Use --github-token or set GITHUB_TOKEN environment variable", "ERROR")
        return False
    
    # GitHub API endpoint for repository dispatch
    url = "https://api.github.com/repos/ChidhagniConsulting/gitops-argocd-apps/dispatches"
    
    headers = {
        'Authorization': f'token {github_token}',
        'Accept': 'application/vnd.github.v3+json',
        'Content-Type': 'application/json'
    }
    
    dispatch_payload = {
        'event_type': 'deploy-to-argocd',
        'client_payload': payload
    }
    
    try:
        print_status("Triggering GitOps deployment...", "INFO")
        response = requests.post(url, headers=headers, json=dispatch_payload)
        
        if response.status_code == 204:
            print_status("GitOps deployment triggered successfully!", "SUCCESS")
            return True
        else:
            print_status(f"Failed to trigger deployment. Status: {response.status_code}", "ERROR")
            print_status(f"Response: {response.text}", "ERROR")
            return False
            
    except Exception as e:
        print_status(f"Error triggering GitOps deployment: {e}", "ERROR")
        return False


def validate_promotion(project_id: str, source_env: str, target_env: str) -> bool:
    """Validate that promotion is safe and allowed"""
    print_status("Validating promotion request...", "INFO")
    
    # Define allowed promotion paths
    allowed_promotions = {
        'dev': ['staging'],
        'staging': ['production'],
        'production': []  # No promotions from production
    }
    
    if target_env not in allowed_promotions.get(source_env, []):
        print_status(f"Invalid promotion path: {source_env} -> {target_env}", "ERROR")
        print_status(f"Allowed promotions from {source_env}: {allowed_promotions.get(source_env, [])}", "INFO")
        return False
    
    # Check if target environment files exist
    target_patch = Path(f"deployments/{project_id}/overlays/{target_env}/patch-image.yaml")
    target_app = Path(f"deployments/{project_id}/overlays/{target_env}/application.yaml")
    
    if not target_patch.exists() or not target_app.exists():
        print_status(f"Target environment {target_env} not configured for project {project_id}", "ERROR")
        return False
    
    print_status("Promotion validation passed", "SUCCESS")
    return True


def generate_manual_commands(payload: Dict[str, str], target_env: str, project_id: str):
    """Generate manual deployment commands"""
    print_status("Manual Deployment Commands:", "INFO")
    print("\n# Option 1: Using repository dispatch (recommended)")
    print("curl -X POST \\")
    print("  -H 'Authorization: token YOUR_GITHUB_TOKEN' \\")
    print("  -H 'Accept: application/vnd.github.v3+json' \\")
    print("  https://api.github.com/repos/ChidhagniConsulting/gitops-argocd-apps/dispatches \\")
    print("  -d '{")
    print('    "event_type": "deploy-to-argocd",')
    print('    "client_payload": ' + json.dumps(payload, indent=6)[:-1] + '    }')
    print("  }'")
    
    print("\n# Option 2: Direct ArgoCD application (after manifests are generated)")
    print(f"kubectl apply -f deployments/{project_id}/argocd/project.yaml")
    print(f"kubectl apply -f deployments/{project_id}/overlays/{target_env}/application.yaml")


def main():
    parser = argparse.ArgumentParser(description='Promote Docker images between environments')
    parser.add_argument('--project-id', required=True, help='Project ID to promote')
    parser.add_argument('--source-env', default='dev', choices=['dev', 'staging'], 
                       help='Source environment (default: dev)')
    parser.add_argument('--target-env', required=True, choices=['staging', 'production'],
                       help='Target environment')
    parser.add_argument('--github-token', help='GitHub token for API access (or set GITHUB_TOKEN env var)')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be done without executing')
    parser.add_argument('--manual', action='store_true', help='Generate manual deployment commands instead of triggering GitOps')
    
    args = parser.parse_args()
    
    # Get GitHub token from environment if not provided
    github_token = args.github_token or os.getenv('GITHUB_TOKEN')
    
    print_status(f"Starting promotion: {args.project_id} from {args.source_env} to {args.target_env}", "INFO")
    
    # Validate promotion
    if not validate_promotion(args.project_id, args.source_env, args.target_env):
        sys.exit(1)
    
    # Get current deployment info
    current_image, app_metadata = get_current_deployment_info(args.project_id, args.source_env)
    if not current_image:
        sys.exit(1)
    
    # Create promotion payload
    payload = create_promotion_payload(current_image, app_metadata, args.target_env)
    
    print_status("Promotion Details:", "INFO")
    print(f"  Project: {payload['project_id']}")
    print(f"  Application: {payload['app_name']}")
    print(f"  Image: {payload['docker_image']}:{payload['docker_tag']}")
    print(f"  Source: {args.source_env} -> Target: {args.target_env}")
    print(f"  Application Type: {payload['application_type']}")
    
    if args.manual:
        generate_manual_commands(payload, args.target_env, args.project_id)
    else:
        # Trigger GitOps deployment
        success = trigger_gitops_deployment(payload, github_token, args.dry_run)
        if not success:
            sys.exit(1)
    
    print_status(f"Promotion workflow completed successfully!", "SUCCESS")


if __name__ == '__main__':
    main()
