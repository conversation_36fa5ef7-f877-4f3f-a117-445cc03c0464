#!/usr/bin/env python3
"""
Cross-platform secret generation utility for Kustomize deployments
Generates secure, random secrets for different application types
"""

import argparse
import base64
import secrets
import string
import hashlib
import os
import sys
from pathlib import Path


def generate_random_string(length=32, include_special=True):
    """Generate a cryptographically secure random string"""
    if include_special:
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    else:
        alphabet = string.ascii_letters + string.digits
    
    return ''.join(secrets.choice(alphabet) for _ in range(length))


def generate_django_secret_key():
    """Generate Django-compatible secret key"""
    # Django secret key should be 50 characters with specific allowed characters
    chars = 'abcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*(-_=+)'
    return ''.join(secrets.choice(chars) for _ in range(50))


def generate_jwt_secret(project_id, environment):
    """Generate JWT secret with project and environment context"""
    # Create a unique but deterministic base for the project
    base = f"{project_id}-{environment}-jwt"
    # Add random component for security
    random_part = generate_random_string(32, include_special=False)
    return f"{base}-{random_part}"


def generate_db_password(project_id, environment):
    """Generate database password"""
    # For managed databases, this might be provided externally
    # For development, generate a secure password
    if environment == 'dev':
        return f"dev_{project_id}_{generate_random_string(16, include_special=False)}"
    else:
        return generate_random_string(24, include_special=True)


def get_app_type_secrets(app_type, project_id, environment):
    """Generate application-type specific secrets"""
    secrets_dict = {}
    
    # Common secrets for all apps
    secrets_dict['JWT_SECRET'] = generate_jwt_secret(project_id, environment)
    secrets_dict['DB_PASSWORD'] = generate_db_password(project_id, environment)
    
    # Application-specific secrets
    if app_type == 'django-backend':
        secrets_dict['DJANGO_SECRET_KEY'] = generate_django_secret_key()
        
    elif app_type == 'springboot-backend':
        # Spring Boot specific secrets
        secrets_dict['SPRING_SECURITY_KEY'] = generate_random_string(32)
        
    elif app_type == 'nest-backend':
        # NestJS specific secrets
        secrets_dict['SESSION_SECRET'] = generate_random_string(32)
        
    elif app_type == 'react-frontend':
        # React apps typically don't need backend secrets
        # But might need API keys for external services
        pass
    
    # Optional secrets (only add if specified)
    return secrets_dict


def encode_secrets_base64(secrets_dict):
    """Encode all secrets to base64"""
    encoded = {}
    for key, value in secrets_dict.items():
        if value:  # Only encode non-empty values
            encoded[f"{key}_B64"] = base64.b64encode(value.encode('utf-8')).decode('utf-8')
    return encoded


def create_secret_patch(project_id, app_type, environment, output_dir=".", 
                       smtp_user=None, smtp_pass=None, 
                       google_client_id=None, google_client_secret=None,
                       custom_secrets=None):
    """Create a Kustomize patch file for secrets"""
    
    # Generate app-type specific secrets
    secrets_dict = get_app_type_secrets(app_type, project_id, environment)
    
    # Add optional secrets if provided
    if smtp_user:
        secrets_dict['SMTP_USER'] = smtp_user
    if smtp_pass:
        secrets_dict['SMTP_PASS'] = smtp_pass
    if google_client_id:
        secrets_dict['GOOGLE_CLIENT_ID'] = google_client_id
    if google_client_secret:
        secrets_dict['GOOGLE_CLIENT_SECRET'] = google_client_secret
    
    # Add custom secrets
    if custom_secrets:
        secrets_dict.update(custom_secrets)
    
    # Encode to base64
    encoded_secrets = encode_secrets_base64(secrets_dict)
    
    # Create patch content
    patch_content = f"""apiVersion: v1
kind: Secret
metadata:
  name: app-secrets
  labels:
    app: {project_id}
    app-type: {app_type}
    environment: {environment}
type: Opaque
data:
"""
    
    # Add all encoded secrets
    for key, value in encoded_secrets.items():
        patch_content += f"  {key.replace('_B64', '')}: {value}\n"
    
    # Add fixed secrets
    patch_content += "  DB_SSL_MODE: cmVxdWlyZQ==  # require\n"
    
    # Write patch file
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    patch_file = output_path / f"secret-patch-{project_id}-{environment}.yaml"
    with open(patch_file, 'w') as f:
        f.write(patch_content)
    
    return patch_file, secrets_dict


def print_secrets_summary(secrets_dict, show_values=False):
    """Print a summary of generated secrets"""
    print("\n🔐 Generated Secrets Summary:")
    print("=" * 40)
    
    for key, value in secrets_dict.items():
        if show_values:
            print(f"  {key}: {value}")
        else:
            print(f"  {key}: {'*' * min(len(value), 20)}")
    
    print("\n⚠️  Store these secrets securely!")
    print("💡 Use --show-values flag to display actual values (not recommended in CI/CD)")


def main():
    parser = argparse.ArgumentParser(description="Generate dynamic secrets for Kustomize deployments")
    parser.add_argument("--project-id", required=True, help="Project identifier")
    parser.add_argument("--app-type", required=True, 
                       choices=["react-frontend", "django-backend", "nest-backend", "springboot-backend", "web-app"],
                       help="Application type")
    parser.add_argument("--environment", required=True, 
                       choices=["dev", "staging", "production"],
                       help="Target environment")
    parser.add_argument("--output-dir", default=".", help="Output directory for patch file")
    
    # Optional secrets
    parser.add_argument("--smtp-user", help="SMTP username")
    parser.add_argument("--smtp-pass", help="SMTP password")
    parser.add_argument("--google-client-id", help="Google OAuth client ID")
    parser.add_argument("--google-client-secret", help="Google OAuth client secret")
    
    # Custom secrets
    parser.add_argument("--custom-secret", action="append", nargs=2, metavar=("KEY", "VALUE"),
                       help="Add custom secret (can be used multiple times)")
    
    # Options
    parser.add_argument("--show-values", action="store_true", 
                       help="Show actual secret values (use with caution)")
    parser.add_argument("--deterministic", action="store_true",
                       help="Generate deterministic secrets for testing (NOT for production)")
    
    args = parser.parse_args()
    
    # Parse custom secrets
    custom_secrets = {}
    if args.custom_secret:
        for key, value in args.custom_secret:
            custom_secrets[key] = value
    
    # Set seed for deterministic generation (testing only)
    if args.deterministic:
        print("⚠️  WARNING: Using deterministic secret generation (for testing only)")
        # Use a deterministic seed based on project info
        seed_string = f"{args.project_id}-{args.app_type}-{args.environment}"
        seed = int(hashlib.md5(seed_string.encode()).hexdigest()[:8], 16)
        # Note: secrets module doesn't support seeding, so we'd need to use random module
        # For now, just warn the user
        print("   Deterministic mode not fully implemented - secrets will still be random")
    
    print(f"🔐 Generating secrets for {args.project_id} ({args.app_type}) in {args.environment}")
    
    try:
        # Generate secrets and create patch
        patch_file, secrets_dict = create_secret_patch(
            project_id=args.project_id,
            app_type=args.app_type,
            environment=args.environment,
            output_dir=args.output_dir,
            smtp_user=args.smtp_user,
            smtp_pass=args.smtp_pass,
            google_client_id=args.google_client_id,
            google_client_secret=args.google_client_secret,
            custom_secrets=custom_secrets
        )
        
        print(f"✅ Secret patch created: {patch_file}")
        
        # Print summary
        print_secrets_summary(secrets_dict, args.show_values)
        
        print(f"\n🚀 Next steps:")
        print(f"1. Add patch to your Kustomize overlay:")
        print(f"   echo '- {patch_file.name}' >> kustomization.yaml")
        print(f"2. Apply the configuration:")
        print(f"   kubectl apply -k .")
        print(f"3. Verify secrets were created:")
        print(f"   kubectl get secret app-secrets -o yaml")
        
    except Exception as e:
        print(f"❌ Error generating secrets: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
