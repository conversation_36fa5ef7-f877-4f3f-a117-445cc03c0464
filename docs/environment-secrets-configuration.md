# Environment-Specific Secrets Configuration

This document outlines the required secrets configuration for each environment in the GitHub Actions CI/CD pipeline.

## 🔐 Repository Secrets

### Core Infrastructure Secrets

These secrets are required for all environments and should be configured at the repository level:

```bash
# GitHub and GitOps
GITOPS_TOKEN                    # GitHub Personal Access Token with repo permissions

# DigitalOcean Infrastructure
DIGITALOCEAN_ACCESS_TOKEN       # DigitalOcean API token for cluster access
```

### Application-Specific Secrets

Configure these secrets based on your application requirements:

#### Spring Boot Backend Secrets
```bash
# Authentication
JWT_SECRET_SPRING               # JWT signing secret for Spring Boot apps

# Database Configuration
ENABLE_DATABASE_SPRING          # Enable/disable database (true/false)
DB_PORT_SPRING                  # Database port (default: 5432)
DB_SSL_MODE_SPRING             # Database SSL mode (require/disable)

# Email Configuration
SMTP_USER_SPRING               # SMTP username for email services
SMTP_PASS_SPRING               # SMTP password for email services

# OAuth Configuration
GOOGLE_CLIENT_ID_SPRING        # Google OAuth client ID
GOOGLE_CLIENT_SECRET_SPRING    # Google OAuth client secret
```

#### Environment-Specific Database Secrets

##### DEV Environment
```bash
DB_HOST_SPRING_DEV             # Development database host
DB_USER_SPRING_DEV             # Development database username
DB_PASSWORD_SPRING_DEV         # Development database password
DB_NAME_SPRING_DEV             # Development database name
```

##### STAGING Environment
```bash
DB_HOST_SPRING_STAGING         # Staging database host
DB_USER_SPRING_STAGING         # Staging database username
DB_PASSWORD_SPRING_STAGING     # Staging database password
DB_NAME_SPRING_STAGING         # Staging database name
```

##### PRODUCTION Environment
```bash
DB_HOST_SPRING_PROD            # Production database host
DB_USER_SPRING_PROD            # Production database username
DB_PASSWORD_SPRING_PROD        # Production database password
DB_NAME_SPRING_PROD            # Production database name
```

## 🌍 GitHub Environment Configuration

### DEV Environment

**Protection Rules:**
- No deployment protection rules
- Auto-deployment enabled on main branch push

**Required Secrets:**
- All core infrastructure secrets
- DEV-specific database secrets

**Environment Variables:**
```bash
ENABLE_AUTO_DEPLOY=true        # Enable automatic deployment
```

### STAGING Environment

**Protection Rules:**
- Optional: Required reviewers (if using GitHub Team plan)
- Manual deployment trigger only

**Required Secrets:**
- All core infrastructure secrets
- STAGING-specific database secrets

**Environment Variables:**
```bash
DEPLOYMENT_WINDOW=business-hours    # Optional: Restrict deployment timing
```

### PRODUCTION Environment

**Protection Rules:**
- Required reviewers (recommended)
- Manual deployment trigger only
- Deployment branch restrictions (main only)

**Required Secrets:**
- All core infrastructure secrets
- PRODUCTION-specific database secrets

**Environment Variables:**
```bash
REQUIRE_STAGING_VERIFICATION=true  # Require staging verification
DEPLOYMENT_WINDOW=maintenance      # Restrict to maintenance windows
```

## 🔧 Setup Instructions

### 1. Repository Secrets Setup

1. Navigate to your repository → Settings → Secrets and variables → Actions
2. Click "New repository secret"
3. Add each required secret with appropriate values

### 2. Environment Secrets Setup

1. Navigate to your repository → Settings → Environments
2. Create/edit each environment (dev, staging, production)
3. Add environment-specific secrets
4. Configure protection rules as needed

### 3. Validation

Use this checklist to verify your secrets configuration:

#### DEV Environment
- [ ] GITOPS_TOKEN configured
- [ ] DIGITALOCEAN_ACCESS_TOKEN configured
- [ ] DB_HOST_SPRING_DEV configured
- [ ] DB_USER_SPRING_DEV configured
- [ ] DB_PASSWORD_SPRING_DEV configured
- [ ] DB_NAME_SPRING_DEV configured
- [ ] ENABLE_AUTO_DEPLOY variable set to 'true'

#### STAGING Environment
- [ ] All DEV secrets available
- [ ] STAGING-specific database secrets configured
- [ ] Environment protection rules configured (optional)

#### PRODUCTION Environment
- [ ] All core secrets available
- [ ] PRODUCTION-specific database secrets configured
- [ ] Environment protection rules configured
- [ ] Required reviewers assigned (recommended)

## 🚨 Security Best Practices

### Secret Management
1. **Use different secrets for each environment**
2. **Rotate secrets regularly**
3. **Use least-privilege access**
4. **Never commit secrets to code**
5. **Use environment-specific database instances**

### Access Control
1. **Limit repository access to necessary team members**
2. **Use GitHub Teams for CODEOWNERS**
3. **Enable branch protection rules**
4. **Require signed commits for production**

### Monitoring
1. **Monitor secret usage in workflow logs**
2. **Set up alerts for failed deployments**
3. **Audit secret access regularly**
4. **Log all production deployments**

## 🔍 Troubleshooting

### Common Issues

#### Secret Not Found
```
Error: Secret GITOPS_TOKEN not found
```
**Solution:** Verify the secret is configured at the correct level (repository vs environment)

#### Permission Denied
```
Error: Permission denied when accessing cluster
```
**Solution:** Check DIGITALOCEAN_ACCESS_TOKEN has correct permissions

#### Database Connection Failed
```
Error: Connection to database failed
```
**Solution:** Verify environment-specific database secrets are correct

### Debug Commands

Check secret availability (without exposing values):
```bash
# In workflow
echo "Checking secrets availability..."
[ -n "$GITOPS_TOKEN" ] && echo "✅ GITOPS_TOKEN available" || echo "❌ GITOPS_TOKEN missing"
[ -n "$DIGITALOCEAN_ACCESS_TOKEN" ] && echo "✅ DIGITALOCEAN_ACCESS_TOKEN available" || echo "❌ DIGITALOCEAN_ACCESS_TOKEN missing"
```

## 📚 Additional Resources

- [GitHub Secrets Documentation](https://docs.github.com/en/actions/security-guides/encrypted-secrets)
- [GitHub Environments Documentation](https://docs.github.com/en/actions/deployment/targeting-different-environments/using-environments-for-deployment)
- [DigitalOcean API Documentation](https://docs.digitalocean.com/reference/api/)
- [ArgoCD Security Best Practices](https://argo-cd.readthedocs.io/en/stable/operator-manual/security/)

## 🔄 Next Steps

After configuring secrets:

1. **Test DEV deployment** - Push to main branch and verify auto-deployment
2. **Test STAGING deployment** - Use manual workflow dispatch
3. **Test PRODUCTION deployment** - Use manual workflow with all approvals
4. **Set up monitoring** - Configure alerts for deployment failures
5. **Document procedures** - Create runbooks for your team
