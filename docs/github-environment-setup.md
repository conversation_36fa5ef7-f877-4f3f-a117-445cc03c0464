# GitHub Environment Setup for Manual Approval Gates

This document explains how to configure GitHub environments to enable manual approval gates for the GitOps deployment pipeline.

## Overview

The deployment pipeline uses GitHub environments to enforce manual approval gates between environment promotions:

- **dev** → **staging** (requires `staging-approval` environment approval)
- **staging** → **production** (requires `production-approval` environment approval)

## ⚠️ Current Configuration

**Manual approval gates are currently DISABLED** for easier initial setup and testing.

The workflow is configured to:
- ✅ **Automatically promote** dev → staging → production
- ⚠️ **No manual approval required** (approval gates commented out)

To enable manual approval gates, see the "Enable Manual Approval Gates" section below.

## Enable Manual Approval Gates

To enable manual approval gates, you need to:

### Step 1: Uncomment Environment Lines in Workflow

Edit `.github/workflows/deploy-from-cicd.yaml` and uncomment these lines:

```yaml
# For staging approval (around line 750):
environment: staging-approval  # Uncomment this line

# For production approval (around line 838):
environment: production-approval  # Uncomment this line
```

### Step 2: Set Up GitHub Environments

## Required GitHub Environment Configuration

### 1. Create Environments

In your GitHub repository, go to **Settings** → **Environments** and create the following environments:

#### staging-approval
- **Environment name**: `staging-approval`
- **Protection rules**:
  - ✅ **Required reviewers**: Add repository maintainers/owners
  - ✅ **Wait timer**: 0 minutes (optional: add delay if needed)
  - ✅ **Deployment branches**: Only protected branches (main)

#### production-approval  
- **Environment name**: `production-approval`
- **Protection rules**:
  - ✅ **Required reviewers**: Add repository maintainers/owners
  - ✅ **Wait timer**: 0 minutes (optional: add delay if needed)
  - ✅ **Deployment branches**: Only protected branches (main)

### 2. Configure Required Reviewers

For each environment, add the GitHub usernames or teams that should approve deployments:

- Repository owners
- DevOps team members
- Senior developers
- Product managers (for production)

### 3. Environment Secrets (Optional)

If you need environment-specific secrets, add them to each environment:

- Database connection strings
- API keys
- Service account credentials

## How Manual Approval Works

### Development Deployment (Automatic)
```
CI/CD Pipeline → Deploy to dev (automatic)
```

### Staging Deployment (Manual Approval Required)
```
Dev deployment success → Staging promotion job waits for approval → Deploy to staging
```

### Production Deployment (Manual Approval Required)
```
Staging deployment success → Production promotion job waits for approval → Deploy to production
```

## Approval Process

### For Staging Promotion
1. After successful dev deployment, the workflow will pause at the `promote-to-staging` job
2. Go to **Actions** tab → Select the workflow run → Click **Review deployments**
3. Select `staging-approval` environment → Click **Approve and deploy**
4. The staging deployment will proceed automatically

### For Production Promotion
1. After successful staging deployment, the workflow will pause at the `promote-to-production` job
2. Go to **Actions** tab → Select the workflow run → Click **Review deployments**
3. Select `production-approval` environment → Click **Approve and deploy**
4. The production deployment will proceed automatically

## Environment-Specific Docker Tags

The pipeline automatically uses environment-specific Docker tags:

- **dev**: `latest` tag
- **staging**: `staging` tag  
- **production**: `production` tag

## Troubleshooting

### Approval Not Showing
- Verify the environment names match exactly: `staging-approval` and `production-approval`
- Check that required reviewers are configured
- Ensure the user has appropriate permissions

### Deployment Stuck
- Check if the environment protection rules are too restrictive
- Verify the branch protection settings allow the workflow to run
- Check if the required reviewers are available

### Environment Not Found
- Ensure environments are created in the correct repository
- Check that environment names are spelled correctly in the workflow
- Verify the workflow is running on the correct branch

## Best Practices

1. **Reviewer Assignment**: Assign multiple reviewers to avoid single points of failure
2. **Documentation**: Keep deployment notes and approval reasons in the GitHub interface
3. **Monitoring**: Set up notifications for pending approvals
4. **Rollback Plan**: Have a rollback strategy documented for each environment
5. **Testing**: Test the approval process with non-critical deployments first

## Security Considerations

- Limit environment access to authorized personnel only
- Use environment-specific secrets for sensitive data
- Enable audit logging for deployment approvals
- Consider using deployment protection rules for additional security
- Implement branch protection rules to prevent unauthorized changes

## Integration with ArgoCD

The manual approval gates work seamlessly with ArgoCD:

1. GitHub workflow generates and commits manifests
2. Manual approval gates control when manifests are generated for each environment
3. ArgoCD automatically syncs the approved manifests to the target clusters
4. Environment-specific configurations are applied automatically

This ensures that only approved changes reach staging and production environments while maintaining GitOps principles.
