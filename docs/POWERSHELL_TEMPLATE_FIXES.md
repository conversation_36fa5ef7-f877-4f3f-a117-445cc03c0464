# PowerShell Template Validation Fixes

## Overview

This document describes the fixes implemented to resolve template validation issues in the PowerShell script `scripts/generate-manifests.ps1` that were causing unprocessed template variables in generated YAML files.

## Root Cause Analysis

The GitHub Actions workflow was failing with validation errors because several template variables were used in templates but not properly defined in the PowerShell script's configuration hashtable. The errors included:

- `{{API_URL}}` - ✅ Already defined
- `{{APP_VERSION}}` - ❌ Missing from config
- `{{PUBLIC_URL}}` - ❌ Missing from config  
- `{{STORAGE_SIZE}}` - ✅ Already defined
- `{{JAVA_XMS}}` - ❌ Missing from config
- `{{JAVA_XMX}}` - ❌ Missing from config
- `{{OAUTH_SCOPES}}` - ✅ Already defined
- `{{SMTP_FROM}}` - ✅ Already defined

## Issues Identified

### 1. Missing Configuration Variables

**Problem**: The PowerShell script was missing several variables in the `$config` hashtable that are referenced in templates.

**Root Cause**: Template variables like `{{APP_VERSION}}`, `{{PUBLIC_URL}}`, `{{JAVA_XMS}}`, and `{{JAVA_XMX}}` were used in templates but not defined in the configuration parsing section.

### 2. Missing Default Values

**Problem**: Even when variables were defined, some lacked appropriate default values, causing empty or undefined values to be processed.

## Fixes Implemented

### 1. Added Missing Configuration Variables

**File**: `scripts/generate-manifests.ps1`

Added missing variables to the `$config` hashtable:

```powershell
# Additional Application Configuration
app_version = Get-IssueValue -Body $IssueBody -FieldName "Application Version"
public_url = Get-IssueValue -Body $IssueBody -FieldName "Public URL"

# JVM Configuration (for Spring Boot applications)
java_xms = Get-IssueValue -Body $IssueBody -FieldName "JVM Initial Heap Size"
java_xmx = Get-IssueValue -Body $IssueBody -FieldName "JVM Maximum Heap Size"
```

### 2. Added Default Values

Added comprehensive default values for all missing variables:

```powershell
# Set default for app_version if not provided
if (-not $config.app_version -or $config.app_version -eq "") {
    $config.app_version = "1.0.0"
}

# Set defaults for JVM configuration
if (-not $config.java_xms -or $config.java_xms -eq "") {
    $config.java_xms = "256m"
}

if (-not $config.java_xmx -or $config.java_xmx -eq "") {
    $config.java_xmx = "512m"
}

# Public URL defaults (application type specific)
if (-not $config.public_url -or $config.public_url -eq "") {
    $config.public_url = "http://localhost:$($config.container_port)"  # React frontend
    # OR
    $config.public_url = "http://localhost:3000"  # Other types
}
```

### 3. Application Type-Specific Defaults

Enhanced the application type-specific configuration to include `public_url` defaults:

```powershell
switch ($config.app_type) {
    "react-frontend" {
        if (-not $config.public_url -or $config.public_url -eq "") {
            $config.public_url = "http://localhost:$($config.container_port)"
        }
    }
    "springboot-backend" {
        if (-not $config.public_url -or $config.public_url -eq "") {
            $config.public_url = "http://localhost:3000"
        }
    }
    default {
        if (-not $config.public_url -or $config.public_url -eq "") {
            $config.public_url = "http://localhost:3000"
        }
    }
}
```

## Variable Mapping

The PowerShell script uses a case-insensitive mapping system where:

- Template variable `{{API_URL}}` maps to `$config.api_url`
- Template variable `{{APP_VERSION}}` maps to `$config.app_version`
- Template variable `{{PUBLIC_URL}}` maps to `$config.public_url`
- Template variable `{{JAVA_XMS}}` maps to `$config.java_xms`
- Template variable `{{JAVA_XMX}}` maps to `$config.java_xmx`

## Testing Results

### Before Fixes
- ❌ Unprocessed template variables: `{{APP_VERSION}}`, `{{PUBLIC_URL}}`, `{{JAVA_XMS}}`, `{{JAVA_XMX}}`
- ❌ YAML syntax errors due to unprocessed variables
- ❌ ArgoCD deployment failures

### After Fixes
- ✅ All template variables properly defined and processed
- ✅ Valid YAML syntax in all generated files
- ✅ Successful template processing for all application types

## Files Modified

1. **`scripts/generate-manifests.ps1`**:
   - Added missing configuration variables: `app_version`, `public_url`, `java_xms`, `java_xmx`
   - Added comprehensive default values for all variables
   - Enhanced application type-specific configuration

## Verification

The fixes were verified using a test script that confirmed all template variables are properly processed:

```powershell
# Test template with previously problematic variables
$testTemplate = "{{API_URL}}, {{APP_VERSION}}, {{PUBLIC_URL}}, {{STORAGE_SIZE}}, {{JAVA_XMS}}, {{JAVA_XMX}}, {{OAUTH_SCOPES}}, {{SMTP_FROM}}"

# Result: All variables successfully processed
# Output: "http://test-api:8080, 1.0.0, http://test-app:3000, 5Gi, 256m, 512m, email,profile,openid, <EMAIL>"
```

## Benefits

1. **Eliminates Template Processing Errors**: No more unprocessed template variables in generated YAML
2. **Reliable ArgoCD Deployments**: Valid YAML syntax ensures successful ArgoCD synchronization
3. **Complete Variable Coverage**: All template variables are now properly defined with sensible defaults
4. **Application Type Awareness**: Different defaults for React frontend vs backend applications

## Resolution Status

✅ **COMPLETE** - All PowerShell template validation issues have been resolved. The GitHub Actions workflow should now generate valid ArgoCD configurations without template processing errors when using the PowerShell script path.
