# Multi-Application Type Deployment Secrets Configuration

This document outlines the required GitHub secrets configuration for deploying different application types (Spring Boot, NestJS, Django) to the staging environment using the `deploy-staging.yml` workflow.

## 🎯 Overview

The staging deployment workflow now supports conditional deployment logic based on application type. Each application type requires its own set of secrets to be configured in your GitHub repository.

## 🔐 Required Secrets by Application Type

### 1. Spring Boot Applications (`springboot-backend`)

#### Core Authentication & Database Secrets
```bash
# Authentication
JWT_SECRET_SPRING                    # JWT signing secret for Spring Boot apps

# Database Configuration  
ENABLE_DATABASE_SPRING              # Enable/disable database (true/false)
DB_HOST_SPRING_STAGING              # Staging database host
DB_USER_SPRING_STAGING              # Staging database username  
DB_PASSWORD_SPRING_STAGING          # Staging database password
DB_NAME_SPRING_STAGING              # Staging database name
DB_PORT_SPRING                      # Database port (default: 5432)
DB_SSL_MODE_SPRING                  # Database SSL mode (require/disable)

# Email Configuration
SMTP_USER_SPRING                    # SMTP username for email services
SMTP_PASS_SPRING                    # SMTP password for email services

# OAuth Configuration
GOOGLE_CLIENT_ID_SPRING             # Google OAuth client ID
GOOGLE_CLIENT_SECRET_SPRING         # Google OAuth client secret
```

### 2. NestJS Applications (`nest-backend`)

#### Core Authentication & Database Secrets
```bash
# Authentication
JWT_SECRET_NEST                     # JWT signing secret for NestJS apps
SESSION_SECRET_NEST                 # Session secret for NestJS session management

# Database Configuration
ENABLE_DATABASE_NEST                # Enable/disable database (true/false)
DB_HOST_NEST_STAGING                # Staging database host
DB_USER_NEST_STAGING                # Staging database username
DB_PASSWORD_NEST_STAGING            # Staging database password
DB_NAME_NEST_STAGING                # Staging database name
DB_PORT_NEST                        # Database port (default: 5432)
DB_SSL_MODE_NEST                    # Database SSL mode (require/disable)

# Email Configuration
SMTP_USER_NEST                      # SMTP username for email services
SMTP_PASS_NEST                      # SMTP password for email services

# OAuth Configuration
GOOGLE_CLIENT_ID_NEST               # Google OAuth client ID
GOOGLE_CLIENT_SECRET_NEST           # Google OAuth client secret
```

### 3. Django Applications (`django-backend`)

#### Core Authentication & Database Secrets
```bash
# Authentication
JWT_SECRET_DJANGO                   # JWT signing secret for Django apps
DJANGO_SECRET_KEY                   # Django framework secret key
SESSION_SECRET_DJANGO               # Session secret for Django session management

# Database Configuration
ENABLE_DATABASE_DJANGO              # Enable/disable database (true/false)
DB_HOST_DJANGO_STAGING              # Staging database host
DB_USER_DJANGO_STAGING              # Staging database username
DB_PASSWORD_DJANGO_STAGING          # Staging database password
DB_NAME_DJANGO_STAGING              # Staging database name
DB_PORT_DJANGO                      # Database port (default: 5432)
DB_SSL_MODE_DJANGO                  # Database SSL mode (require/disable)

# Email Configuration
SMTP_USER_DJANGO                    # SMTP username for email services
SMTP_PASS_DJANGO                    # SMTP password for email services

# OAuth Configuration
GOOGLE_CLIENT_ID_DJANGO             # Google OAuth client ID
GOOGLE_CLIENT_SECRET_DJANGO         # Google OAuth client secret

# Django-Specific Configuration
RATE_LIMIT_WINDOW_MS_DJANGO         # Rate limiting window in milliseconds
RATE_LIMIT_MAX_REQUESTS_DJANGO      # Maximum requests per window
PASSWORD_RESET_TOKEN_EXPIRY_DJANGO  # Password reset token expiry time
EMAIL_VERIFICATION_TOKEN_EXPIRY_DJANGO # Email verification token expiry time
```

## 🛠️ Setup Instructions

### Step 1: Navigate to Repository Secrets
1. Go to your GitHub repository
2. Click on **Settings** → **Secrets and variables** → **Actions**
3. Click **New repository secret**

### Step 2: Add Application-Specific Secrets
Based on your application type, add the corresponding secrets from the lists above.

#### For Spring Boot Applications:
Add all secrets with the `_SPRING` suffix.

#### For NestJS Applications:
Add all secrets with the `_NEST` suffix.

#### For Django Applications:
Add all secrets with the `_DJANGO` suffix.

### Step 3: Core Infrastructure Secrets (Required for All)
```bash
# GitHub and GitOps
GITOPS_TOKEN                        # GitHub Personal Access Token with repo permissions

# DigitalOcean Infrastructure  
DIGITALOCEAN_ACCESS_TOKEN           # DigitalOcean API token for cluster access
```

## 🚀 Usage Examples

### Deploy Spring Boot Application
```yaml
# Workflow dispatch inputs:
project_id: "my-spring-app"
docker_image: "registry.digitalocean.com/my-registry/spring-app"
docker_tag: "v1.0.0"
container_port: "8080"
application_type: "springboot-backend"
```

### Deploy NestJS Application
```yaml
# Workflow dispatch inputs:
project_id: "my-nest-app"
docker_image: "registry.digitalocean.com/my-registry/nest-app"
docker_tag: "v1.0.0"
container_port: "3000"
application_type: "nest-backend"
```

### Deploy Django Application
```yaml
# Workflow dispatch inputs:
project_id: "my-django-app"
docker_image: "registry.digitalocean.com/my-registry/django-app"
docker_tag: "v1.0.0"
container_port: "8000"
application_type: "django-backend"
```

## 🔍 Validation

The workflow automatically validates that the required secrets are available for the selected application type. If any required secrets are missing, the deployment will fail with a clear error message.

## 🔄 Migration from Single Application Type

If you're migrating from the previous Spring-only configuration:

1. **Keep existing Spring secrets** - They will continue to work for Spring Boot applications
2. **Add new application type secrets** - Only add secrets for the new application types you plan to deploy
3. **Test with a non-production deployment** - Verify the new configuration works before production use

## 🚨 Security Best Practices

1. **Use different database instances** for each application type in staging
2. **Rotate secrets regularly** for all application types
3. **Use least-privilege access** for database users
4. **Monitor secret usage** in workflow logs
5. **Never commit secrets to code** - Always use GitHub secrets

## 🔧 Troubleshooting

### Common Issues

#### Missing Required Secrets
```
Error: Required secret JWT_SECRET_NEST not found for nest-backend application
```
**Solution:** Add the missing secret to your repository secrets with the exact name shown.

#### Wrong Application Type
```
Warning: Unknown application type: custom-app, using default Spring Boot configuration
```
**Solution:** Use one of the supported application types: `springboot-backend`, `nest-backend`, `django-backend`.

#### Database Connection Failed
```
Error: Connection to staging database failed
```
**Solution:** Verify the staging database secrets are correct for your application type.

## 📚 Related Documentation

- [Environment-Specific Secrets Configuration](./environment-secrets-configuration.md)
- [GitHub Secrets Documentation](https://docs.github.com/en/actions/security-guides/encrypted-secrets)
- [ArgoCD Security Best Practices](https://argo-cd.readthedocs.io/en/stable/operator-manual/security/)
