# Startup CI/CD Environment Setup Guide

This guide provides step-by-step instructions for setting up the comprehensive GitHub Actions CI/CD workflow with environment-based approvals for your startup repository.

## 📋 Overview

The implemented solution provides:
- **DEV Environment**: Auto-deploy on every push to main branch (no approvals)
- **STAGING Environment**: Manual deployment trigger with optional approval workflow
- **PROD Environment**: Strict manual deployment with simulated approval process using CODEOWNERS

## 🚀 Quick Start

### Prerequisites
- GitHub repository with Team plan (for environment protection features)
- DigitalOcean account with Kubernetes cluster access
- Repository admin permissions

### Implementation Status
✅ **Completed Automatically:**
- Three workflow files created (`.github/workflows/deploy-*.yml`)
- Enhanced CODEOWNERS file for production approval simulation
- Environment-specific secrets documentation

⚠️ **Manual Setup Required:**
- GitHub Environment creation and configuration
- Repository secrets configuration
- Branch protection rules setup
- Testing and validation

## 🔧 Manual Setup Steps

### Step 1: Create GitHub Environments

1. **Navigate to Repository Settings**
   ```
   Your Repository → Settings → Environments
   ```

2. **Create DEV Environment**
   - Click "New environment"
   - Name: `dev`
   - **Protection rules:** None (auto-deployment)
   - **Environment secrets:** Add DEV-specific secrets
   - **Variables:** 
     - `ENABLE_AUTO_DEPLOY` = `true`

3. **Create STAGING Environment**
   - Click "New environment"
   - Name: `staging`
   - **Protection rules:** 
     - ✅ Required reviewers (optional, 1-2 team members)
     - ✅ Wait timer: 0 minutes
   - **Environment secrets:** Add STAGING-specific secrets

4. **Create PRODUCTION Environment**
   - Click "New environment"
   - Name: `production`
   - **Protection rules:**
     - ✅ Required reviewers (1-2 senior team members)
     - ✅ Deployment branches: Selected branches → `main`
     - ✅ Wait timer: 5 minutes (optional)
   - **Environment secrets:** Add PRODUCTION-specific secrets

### Step 2: Configure Repository Secrets

1. **Navigate to Repository Secrets**
   ```
   Your Repository → Settings → Secrets and variables → Actions
   ```

2. **Add Core Infrastructure Secrets**
   ```bash
   GITOPS_TOKEN                    # GitHub PAT with repo permissions
   DIGITALOCEAN_ACCESS_TOKEN       # DigitalOcean API token
   ```

3. **Add Application Secrets** (example for Spring Boot)
   ```bash
   # Authentication
   JWT_SECRET_SPRING
   
   # Database Configuration
   ENABLE_DATABASE_SPRING
   DB_PORT_SPRING
   DB_SSL_MODE_SPRING
   
   # Email Configuration
   SMTP_USER_SPRING
   SMTP_PASS_SPRING
   
   # OAuth Configuration
   GOOGLE_CLIENT_ID_SPRING
   GOOGLE_CLIENT_SECRET_SPRING
   ```

4. **Add Environment-Specific Database Secrets**
   ```bash
   # DEV Environment
   DB_HOST_SPRING_DEV
   DB_USER_SPRING_DEV
   DB_PASSWORD_SPRING_DEV
   DB_NAME_SPRING_DEV
   
   # STAGING Environment
   DB_HOST_SPRING_STAGING
   DB_USER_SPRING_STAGING
   DB_PASSWORD_SPRING_STAGING
   DB_NAME_SPRING_STAGING
   
   # PRODUCTION Environment
   DB_HOST_SPRING_PROD
   DB_USER_SPRING_PROD
   DB_PASSWORD_SPRING_PROD
   DB_NAME_SPRING_PROD
   ```

### Step 3: Configure Branch Protection Rules

1. **Navigate to Branch Protection**
   ```
   Your Repository → Settings → Branches
   ```

2. **Add Rule for Main Branch**
   - Branch name pattern: `main`
   - **Protection settings:**
     - ✅ Require a pull request before merging
     - ✅ Require approvals: 1
     - ✅ Dismiss stale PR approvals when new commits are pushed
     - ✅ Require review from CODEOWNERS
     - ✅ Require status checks to pass before merging
     - ✅ Require branches to be up to date before merging
     - ✅ Require conversation resolution before merging
     - ✅ Include administrators

### Step 4: Configure Repository Variables

1. **Navigate to Repository Variables**
   ```
   Your Repository → Settings → Secrets and variables → Actions → Variables tab
   ```

2. **Add Required Variables**
   ```bash
   ENABLE_AUTO_DEPLOY = true      # Enable auto-deployment for DEV
   ```

### Step 5: Verify CODEOWNERS Configuration

1. **Check CODEOWNERS File**
   - Verify `.github/CODEOWNERS` exists and contains your team members
   - Update usernames to match your actual GitHub usernames
   - Example:
     ```
     # Production environment protection
     deployments/*/overlays/production/ @your-username @team-lead
     .github/workflows/ @your-username @devops-team
     ```

## 🧪 Testing Instructions

### Test 1: DEV Environment Auto-Deployment

1. **Create a test deployment manifest**
   ```bash
   mkdir -p deployments/test-app/overlays/dev
   # Add your application manifests
   ```

2. **Commit and push to main**
   ```bash
   git add deployments/test-app/
   git commit -m "Add test application for DEV environment"
   git push origin main
   ```

3. **Verify auto-deployment**
   - Check Actions tab for "🚀 Deploy to DEV Environment" workflow
   - Verify it runs automatically
   - Check ArgoCD dashboard for deployed application

### Test 2: STAGING Environment Manual Deployment

1. **Navigate to Actions Tab**
   ```
   Your Repository → Actions → 🎯 Deploy to STAGING Environment
   ```

2. **Run Workflow Manually**
   - Click "Run workflow"
   - Fill in required parameters:
     - Project ID: `test-app`
     - Docker image: `your-registry/test-app`
     - Docker tag: `staging`
     - Application type: `web-app`
   - Click "Run workflow"

3. **Verify Approval Process**
   - Check if approval is required (based on environment settings)
   - Approve if prompted
   - Verify deployment completes successfully

### Test 3: PRODUCTION Environment Strict Deployment

1. **Navigate to Actions Tab**
   ```
   Your Repository → Actions → 🔒 Deploy to PRODUCTION Environment
   ```

2. **Run Workflow Manually**
   - Click "Run workflow"
   - Fill in required parameters:
     - Project ID: `test-app`
     - Docker image: `your-registry/test-app`
     - Docker tag: `production`
     - Application type: `web-app`
     - Staging verification: `true`
   - Click "Run workflow"

3. **Verify Multi-Stage Approval**
   - Security & Compliance Approval gate
   - CODEOWNER Approval Simulation
   - Final deployment with production safeguards

## 🔍 Validation Checklist

### Environment Configuration
- [ ] DEV environment created with auto-deployment enabled
- [ ] STAGING environment created with optional approvals
- [ ] PRODUCTION environment created with strict protection
- [ ] All required secrets configured for each environment

### Workflow Testing
- [ ] DEV auto-deployment works on main branch push
- [ ] STAGING manual deployment works with approval flow
- [ ] PRODUCTION deployment requires all approval gates
- [ ] Failed deployments show appropriate error messages

### Security Validation
- [ ] Branch protection rules prevent direct pushes to main
- [ ] CODEOWNERS file requires approval for production changes
- [ ] Secrets are properly scoped to environments
- [ ] Production deployments have additional safety checks

## 🚨 Troubleshooting

### Common Issues

#### 1. Workflow Not Triggering
**Problem:** DEV workflow doesn't run on push to main
**Solution:** 
- Check if `ENABLE_AUTO_DEPLOY` variable is set to `true`
- Verify the push includes changes to `deployments/` directory
- Check workflow file syntax

#### 2. Secret Not Found Error
**Problem:** `Error: Secret GITOPS_TOKEN not found`
**Solution:**
- Verify secret is configured at repository level
- Check secret name spelling
- Ensure environment has access to repository secrets

#### 3. Environment Not Found
**Problem:** `Environment 'staging' not found`
**Solution:**
- Create the environment in repository settings
- Verify environment name matches workflow file
- Check environment protection rules

#### 4. Approval Required But No Reviewers
**Problem:** Workflow waiting for approval but no reviewers assigned
**Solution:**
- Add required reviewers to environment settings
- Check if reviewers have repository access
- Verify CODEOWNERS file configuration

### Debug Commands

Add these to workflow for debugging:
```yaml
- name: Debug Environment
  run: |
    echo "Environment: ${{ github.environment }}"
    echo "Actor: ${{ github.actor }}"
    echo "Repository: ${{ github.repository }}"
    echo "Workflow: ${{ github.workflow }}"
```

## 📚 Next Steps

After successful setup:

1. **Customize for Your Stack**
   - Modify workflows for your specific application types
   - Add environment-specific configurations
   - Integrate with your monitoring tools

2. **Enhance Security**
   - Set up secret rotation procedures
   - Configure audit logging
   - Add security scanning to workflows

3. **Scale the Process**
   - Create templates for new applications
   - Automate environment provisioning
   - Add integration tests to deployment pipeline

4. **Monitor and Improve**
   - Set up deployment metrics
   - Create alerting for failed deployments
   - Gather team feedback and iterate

## 🔗 Related Documentation

- [Environment Secrets Configuration](./environment-secrets-configuration.md)
- [Testing Instructions](./startup-cicd-testing-guide.md)
- [GitHub Environments Documentation](https://docs.github.com/en/actions/deployment/targeting-different-environments/using-environments-for-deployment)
- [CODEOWNERS Documentation](https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/customizing-your-repository/about-code-owners)
