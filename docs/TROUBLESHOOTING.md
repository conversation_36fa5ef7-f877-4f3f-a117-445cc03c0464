# 🔧 Troubleshooting Guide

This guide helps resolve common issues with the self-service GitOps automation system.

## 🚀 Quick Test Before Troubleshooting

**Run the local test suite first:**
```powershell
# Test the entire workflow locally
./tests/test-workflow.ps1

# Test with cleanup
./tests/test-workflow.ps1 -Cleanup

# Test with custom issue file
./tests/test-workflow.ps1 -TestIssueFile "my-issue.txt" -OutputDir "./my-test"
```

## 🚨 Common Issues

### Issue: Workflow Not Triggering

**Symptoms:**
- Created an issue using the deployment template
- GitHub Actions workflow shows "This job was skipped"
- No manifest generation occurs

**Quick Fix - Use the Simple Workflow:**
The repository now includes a backup workflow (`simple-deploy.yaml`) that uses simpler trigger conditions. If the main workflow fails, this should work.

**Causes & Solutions:**

#### 1. Missing or Incorrect Label
**Problem:** The `app-deployment` label is not applied to the issue.

**Solution:**
```bash
# Check if the issue has the correct label
# Go to your issue and verify it has the "app-deployment" label

# If missing, manually add the label:
# 1. Go to the issue page
# 2. Click "Labels" on the right sidebar
# 3. Add "app-deployment" label
# 4. The workflow should trigger automatically
```

#### 2. Issue Title Format
**Problem:** The issue title doesn't start with `[DEPLOY]`.

**Solution:**
- Edit the issue title to start with `[DEPLOY]`
- Or ensure you used the correct issue template

#### 3. Repository Permissions
**Problem:** GitHub Actions doesn't have sufficient permissions.

**Solution:**
```yaml
# Check repository settings:
# 1. Go to Settings → Actions → General
# 2. Ensure "Read and write permissions" is enabled
# 3. Check "Allow GitHub Actions to create and approve pull requests"
```

#### 4. Workflow File Issues
**Problem:** The workflow file has syntax errors.

**Solution:**
```bash
# Check workflow file syntax
# Go to Actions tab → Click on the workflow → Check for errors
```

### Issue: Validation Errors

**Symptoms:**
- Workflow runs but fails during validation
- Error messages about invalid input

**Common Validation Errors:**

#### Invalid Project Identifier
```
❌ Project Identifier must be lowercase alphanumeric with hyphens only
```
**Solution:** Use only lowercase letters, numbers, and hyphens (e.g., `my-app-name`)

#### Invalid Resource Format
```
❌ Memory Request must be in format like '256Mi', '1Gi', '2Ti'
```
**Solution:** Include units with memory values (Mi, Gi, Ti)

#### Missing Required Fields
```
❌ Container Image is required
```
**Solution:** Fill all fields marked as required in the issue template

### Issue: Manifest Generation Fails

**Symptoms:**
- Validation passes but manifest generation fails
- PowerShell script errors

**Solutions:**

#### 1. Check Script Permissions
```bash
# Ensure scripts are executable
chmod +x scripts/generate-manifests.ps1
chmod +x scripts/validate-deployment.ps1
```

#### 2. Template Issues
```bash
# Verify templates exist and are valid
find templates -name "*.yaml" -exec yamllint {} \;
```

#### 3. PowerShell Version
```bash
# Check PowerShell version in workflow
# The workflow installs PowerShell 7.3.6
# Ensure scripts are compatible
```

### Issue: Generated Files Are Incorrect

**Symptoms:**
- Files are generated but contain errors
- Missing or incorrect values in manifests

**Solutions:**

#### 1. Check Issue Body Format
Ensure the issue body follows the expected format:
```
### Application Name
My App

### Project Identifier
my-app

### Container Image
docker.io/myorg/my-app:v1.0.0
```

#### 2. Checkbox Format
For checkboxes, ensure they appear as:
```
### Enable PostgreSQL Database
- [x] Include PostgreSQL database deployment
```

#### 3. Environment Variables Format
```
### Environment Variables
NODE_ENV=production
API_VERSION=v1
LOG_LEVEL=info
```

## 🔍 Debugging Steps

### 1. Check Workflow Logs
```bash
# Go to Actions tab in your repository
# Click on the failed workflow run
# Expand each step to see detailed logs
# Look for error messages and stack traces
```

### 2. Validate Locally
```bash
# Test validation locally
./scripts/validate-deployment.ps1 -IssueBody "$(cat test-issue.txt)" -Detailed

# Test manifest generation locally
./scripts/generate-manifests.ps1 -IssueBody "$(cat test-issue.txt)" -DryRun
```

### 3. Check Repository Structure
```bash
# Ensure all required files exist
ls -la .github/ISSUE_TEMPLATE/
ls -la .github/workflows/
ls -la scripts/
ls -la templates/
```

### 4. Verify Permissions
```bash
# Check if the GitHub token has required permissions
# Repository Settings → Actions → General → Workflow permissions
```

## 🛠️ Manual Fixes

### Force Trigger Workflow
If the workflow doesn't trigger automatically:

1. **Add Label Manually:**
   - Go to the issue
   - Add `app-deployment` label
   - Workflow should trigger

2. **Edit Issue:**
   - Make a small edit to the issue body
   - Save the issue
   - This triggers the `edited` event

3. **Re-run Workflow:**
   - Go to Actions tab
   - Find the skipped workflow
   - Click "Re-run jobs"

### Fix Generated Manifests
If manifests are generated but incorrect:

1. **Edit Files Directly:**
   ```bash
   # Edit the generated files manually
   vim {project-name}/k8s/deployment.yaml
   
   # Commit changes
   git add {project-name}/
   git commit -m "Fix manifest issues"
   git push
   ```

2. **Regenerate:**
   - Delete the project directory
   - Edit the issue to trigger regeneration
   - Or run the script locally and commit

### Update Secrets
Always update secret placeholders:

```bash
# Edit secret file
vim {project-name}/k8s/secret.yaml

# Replace PLACEHOLDER values with actual base64-encoded secrets
echo -n "your-secret-value" | base64

# Update the file with real values
```

## 📞 Getting Help

### 1. Check Logs First
Always check the workflow logs for specific error messages.

### 2. Validate Input
Use the validation script to check your input format:
```bash
./scripts/validate-deployment.ps1 -IssueBody "$(cat your-issue.txt)" -Detailed
```

### 3. Test Locally
Run the generation script locally to debug:
```bash
./scripts/generate-manifests.ps1 -IssueBody "$(cat your-issue.txt)" -OutputDir "./test-output"
```

### 4. Create Support Issue
If problems persist:
1. Create a new issue with label `support`
2. Include:
   - Link to the original deployment issue
   - Workflow run URL
   - Error messages from logs
   - What you've already tried

### 5. Check Documentation
- [GitHub Issue Forms Documentation](https://docs.github.com/en/communities/using-templates-to-encourage-useful-issues-and-pull-requests/syntax-for-issue-forms)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [ArgoCD Documentation](https://argo-cd.readthedocs.io/)

## 🔄 Recovery Procedures

### Complete Reset
If everything is broken:

1. **Delete Generated Files:**
   ```bash
   rm -rf {project-name}/
   ```

2. **Fix Issue Template:**
   - Check `.github/ISSUE_TEMPLATE/app-deployment.yml`
   - Validate YAML syntax

3. **Fix Workflow:**
   - Check `.github/workflows/generate-app-manifests.yaml`
   - Validate workflow syntax

4. **Test Scripts:**
   ```bash
   ./tests/run-local-tests.sh
   ```

5. **Create New Issue:**
   - Use the corrected template
   - Verify all fields are filled correctly

### Rollback Changes
If recent changes broke the system:

```bash
# Revert to last working commit
git log --oneline
git revert <commit-hash>
git push
```

---

**Need immediate help?** Create an issue with the `support` label and include all relevant error messages and logs.
