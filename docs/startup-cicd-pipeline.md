# Startup CI/CD Pipeline Documentation

## Overview

This document describes the comprehensive GitHub CI/CD pipeline designed for startup applications with a main-branch-only strategy and environment-specific deployment controls.

## Architecture

### Environment Strategy
- **Development**: Removed from automated pipeline (manual testing only)
- **Staging**: Manual deployment via workflow_dispatch with tagged commits
- **Production**: Restricted deployment with approval gates and release manager authorization

### Branch Strategy
- **Main Branch Only**: Single long-lived branch for all development
- **Feature Branches**: Short-lived branches merged via Pull Requests
- **No Environment Branches**: No develop, staging, or production branches

## Deployment Workflows

### 1. Staging Environment Deployment

**File**: `.github/workflows/staging-deploy.yml`

**Trigger**: Manual via workflow_dispatch

**Requirements**:
- Tagged commits only (semantic versioning: v1.0.0, v1.2.3, etc.)
- Docker image must exist in registry
- Staging environment secrets configured

**Process**:
1. Validate deployment request and tag format
2. Check Docker image availability in registry
3. Verify staging cluster connectivity
4. Deploy to staging environment via GitOps
5. Monitor deployment status

**Usage**:
```bash
# Navigate to Actions tab in GitHub
# Select "Staging Environment Deployment"
# Fill in required inputs:
# - Project ID: ai-spring-backend
# - Git Tag: v1.0.0
# - Docker Tag: (optional, defaults to 1.0.0)
# - Force Deploy: false (optional)
```

### 2. Production Environment Deployment

**File**: `.github/workflows/prod-deploy.yml`

**Trigger**: Manual via workflow_dispatch (restricted users only)

**Authorization**: Only @AshrafSyed25 can trigger production deployments

**Requirements**:
- GitHub release must exist for the tag
- Semantic versioning (no pre-release tags)
- Deployment reason (audit requirement)
- Rollback plan (mandatory)
- Docker image must exist in registry

**Process**:
1. Validate user authorization (AshrafSyed25 only)
2. Verify GitHub release exists
3. Check Docker image availability
4. Validate staging deployment (recommended)
5. Manual approval gate via GitHub Environment
6. Deploy to production via GitOps
7. Monitor deployment and provide rollback guidance

**Usage**:
```bash
# Navigate to Actions tab in GitHub
# Select "Production Environment Deployment"
# Fill in required inputs:
# - Project ID: ai-spring-backend
# - Release Tag: v1.0.0
# - Docker Tag: (optional, defaults to 1.0.0)
# - Deployment Reason: "Critical bug fix for user authentication"
# - Rollback Plan: "Revert to v0.9.5 using previous workflow run"
```

## Security and Governance

### GitHub Environments

**Staging Environment**:
- Environment protection rules enabled
- Deployment audit logs maintained
- Environment-specific secrets

**Production Environment**:
- Restricted to authorized users only
- Manual approval required
- Enhanced audit logging
- Environment-specific secrets

### CODEOWNERS Protection

Protected files requiring @AshrafSyed25 approval:
- `.github/workflows/prod-deploy.yml` - Production deployment workflow
- `.github/workflows/staging-deploy.yml` - Staging deployment workflow
- `.github/workflows/` - All workflow files
- `manifests/` - Kubernetes manifest templates
- `scripts/deploy.py` - Core deployment scripts
- `.github/CODEOWNERS` - This protection file itself

### Branch Protection Rules

Main branch protection (to be configured):
- Require pull request reviews before merging
- Require status checks to pass
- Disallow force pushes and deletions
- Require up-to-date branches before merging

## Required Secrets Configuration

### Staging Environment Secrets
```
JWT_SECRET_STAGING
ENABLE_DATABASE_STAGING
DB_HOST_STAGING
DB_USER_STAGING
DB_PASSWORD_STAGING
DB_NAME_STAGING
DB_PORT_STAGING
DB_SSL_MODE_STAGING
SMTP_USER_STAGING
SMTP_PASS_STAGING
GOOGLE_CLIENT_ID_STAGING
GOOGLE_CLIENT_SECRET_STAGING
```

### Production Environment Secrets
```
JWT_SECRET_PRODUCTION
ENABLE_DATABASE_PRODUCTION
DB_HOST_PRODUCTION
DB_USER_PRODUCTION
DB_PASSWORD_PRODUCTION
DB_NAME_PRODUCTION
DB_PORT_PRODUCTION
DB_SSL_MODE_PRODUCTION
SMTP_USER_PRODUCTION
SMTP_PASS_PRODUCTION
GOOGLE_CLIENT_ID_PRODUCTION
GOOGLE_CLIENT_SECRET_PRODUCTION
```

### Repository Variables
```
STAGING_CLUSTER_ID - DigitalOcean Kubernetes cluster ID for staging
PRODUCTION_CLUSTER_ID - DigitalOcean Kubernetes cluster ID for production
```

## Application Types Supported

The pipeline automatically detects application types based on project naming:

- `*-react-frontend` → react-frontend (port 3000, health check: /)
- `*-spring-backend` → springboot-backend (port 8080, health check: /actuator/health)
- `*-django-backend` → django-backend (port 8000, health check: /health/)
- `*-nest-backend` → nest-backend (port 3000, health check: /health)
- Other → web-app (port 8080, health check: /health)

## Deployment Process

### Staging Deployment Process

1. **Preparation**:
   - Create and push a git tag: `git tag v1.0.0 && git push origin v1.0.0`
   - Ensure Docker image is built and pushed to registry
   - Verify staging secrets are configured

2. **Deployment**:
   - Navigate to GitHub Actions → "Staging Environment Deployment"
   - Enter project ID and git tag
   - Click "Run workflow"
   - Monitor deployment progress

3. **Validation**:
   - Check ArgoCD dashboard for deployment status
   - Verify application health in staging environment
   - Run staging tests and validation

### Production Deployment Process

1. **Prerequisites**:
   - Successful staging deployment and validation
   - Create GitHub release for the tag
   - Prepare deployment reason and rollback plan

2. **Release Creation**:
   ```bash
   # Create GitHub release
   gh release create v1.0.0 --title "Release v1.0.0" --notes "Production release with bug fixes"
   ```

3. **Deployment**:
   - Navigate to GitHub Actions → "Production Environment Deployment"
   - Enter all required fields including deployment reason and rollback plan
   - Click "Run workflow" (only available to @AshrafSyed25)
   - Approve deployment in GitHub Environment

4. **Monitoring**:
   - Monitor ArgoCD dashboard
   - Check application metrics and logs
   - Verify production functionality
   - Update documentation and release notes

## Troubleshooting

### Common Issues

**Staging Deployment Fails**:
- Verify git tag exists and follows semantic versioning
- Check Docker image exists in registry
- Ensure staging secrets are properly configured
- Verify staging cluster connectivity

**Production Deployment Unauthorized**:
- Only @AshrafSyed25 can trigger production deployments
- Ensure user is properly authenticated

**Docker Image Not Found**:
- Verify image was built and pushed to DigitalOcean Container Registry
- Check image tag matches the deployment tag
- Ensure registry authentication is working

**GitHub Release Not Found**:
- Create GitHub release before production deployment
- Ensure release tag matches deployment tag
- Verify release is published (not draft)

### Manual Rollback

If automatic rollback is needed:

1. **Identify Previous Version**:
   ```bash
   # Check deployment history
   kubectl get applications -n argocd
   kubectl describe application <project-id> -n argocd
   ```

2. **Rollback via ArgoCD**:
   - Use ArgoCD UI to rollback to previous version
   - Or trigger new deployment with previous tag

3. **Emergency Rollback**:
   ```bash
   # Direct kubectl rollback (emergency only)
   kubectl rollout undo deployment/<project-id> -n <project-id>-production
   ```

## Operational Procedures

### Regular Maintenance

1. **Weekly**:
   - Review deployment logs and metrics
   - Check for failed deployments
   - Validate backup and rollback procedures

2. **Monthly**:
   - Review and update secrets rotation
   - Audit deployment permissions and access
   - Update documentation as needed

3. **Quarterly**:
   - Review and update security policies
   - Validate disaster recovery procedures
   - Update CI/CD pipeline dependencies

### Monitoring and Alerting

- Monitor ArgoCD dashboard for deployment status
- Set up alerts for failed deployments
- Monitor application health checks
- Track deployment frequency and success rates

## Next Steps

After implementing this pipeline, consider:

1. **Enhanced Monitoring**: Implement comprehensive monitoring and alerting
2. **Automated Testing**: Add automated testing stages to workflows
3. **Security Scanning**: Integrate security scanning into the pipeline
4. **Performance Testing**: Add performance testing for staging deployments
5. **Disaster Recovery**: Implement and test disaster recovery procedures
