# Testing Application Types Guide

This guide provides comprehensive testing scenarios for the React Frontend and Spring Boot Backend application types in the GitOps automation system.

## Quick Test Commands

### Test React Frontend Template

```bash
# Using GitHub Issue Template (Manual)
# Create issue with:
# - Application Type: react-frontend
# - Container Port: 80
# - Health Check Path: /

# Using Repository Dispatch (CI/CD)
curl -X POST \
  -H "Authorization: token $GITHUB_TOKEN" \
  -H "Accept: application/vnd.github.v3+json" \
  https://api.github.com/repos/ChidhagniConsulting/gitops-argocd-apps/dispatches \
  -d '{
    "event_type": "deploy-to-argocd",
    "client_payload": {
      "app_name": "Test React App",
      "project_id": "test-react-app",
      "application_type": "react-frontend",
      "environment": "dev",
      "docker_image": "nginx",
      "docker_tag": "alpine",
      "source_repo": "test/react-app",
      "source_branch": "main",
      "commit_sha": "test123"
    }
  }'
```

### Test Spring Boot Backend Template

```bash
# Using Repository Dispatch (CI/CD)
curl -X POST \
  -H "Authorization: token $GITHUB_TOKEN" \
  -H "Accept: application/vnd.github.v3+json" \
  https://api.github.com/repos/ChidhagniConsulting/gitops-argocd-apps/dispatches \
  -d '{
    "event_type": "deploy-to-argocd",
    "client_payload": {
      "app_name": "Test Spring Boot API",
      "project_id": "test-spring-api",
      "application_type": "springboot-backend",
      "environment": "dev",
      "docker_image": "openjdk",
      "docker_tag": "11-jre-slim",
      "source_repo": "test/spring-api",
      "source_branch": "main",
      "commit_sha": "test456"
    }
  }'
```

## Local Testing with Scripts

### PowerShell Script Testing

```powershell
# Test React Frontend
./scripts/generate-manifests-cicd.ps1 `
  -AppName "Test React App" `
  -ProjectId "test-react-app" `
  -AppType "react-frontend" `
  -Environment "dev" `
  -DockerImage "nginx" `
  -DockerTag "alpine" `
  -OutputDir "./test-output"

# Test Spring Boot Backend
./scripts/generate-manifests-cicd.ps1 `
  -AppName "Test Spring Boot API" `
  -ProjectId "test-spring-api" `
  -AppType "springboot-backend" `
  -Environment "dev" `
  -DockerImage "openjdk" `
  -DockerTag "11-jre-slim" `
  -OutputDir "./test-output"
```

### Python Script Testing

```bash
# Test React Frontend
python scripts/generate-manifests-cicd.py \
  --app-name "Test React App" \
  --project-id "test-react-app" \
  --application-type "react-frontend" \
  --environment "dev" \
  --docker-image "nginx" \
  --docker-tag "alpine" \
  --output-dir "./test-output"

# Test Spring Boot Backend
python scripts/generate-manifests-cicd.py \
  --app-name "Test Spring Boot API" \
  --project-id "test-spring-api" \
  --application-type "springboot-backend" \
  --environment "dev" \
  --docker-image "openjdk" \
  --docker-tag "11-jre-slim" \
  --output-dir "./test-output"
```

## Validation Checklist

### React Frontend Validation

**Generated Files:**
- [ ] `test-react-app/argocd/application.yaml` - Contains `app-type: react-frontend`
- [ ] `test-react-app/argocd/project.yaml` - Project configuration
- [ ] `test-react-app/k8s/namespace.yaml` - Contains `app-type: react-frontend` label
- [ ] `test-react-app/k8s/deployment.yaml` - React-specific configuration
- [ ] `test-react-app/k8s/service.yaml` - Service on port 80
- [ ] `test-react-app/k8s/configmap.yaml` - React environment variables
- [ ] `test-react-app/k8s/secret.yaml` - Minimal secrets

**Configuration Validation:**
- [ ] Container port: 80 (not 8080)
- [ ] Health check path: "/" (not "/actuator/health")
- [ ] Memory request: 128Mi (lower than backend)
- [ ] CPU request: 50m (lower than backend)
- [ ] No database init containers
- [ ] No database environment variables in ConfigMap
- [ ] REACT_APP_* environment variables present
- [ ] Minimal secrets (no JWT/DB/SMTP secrets)

### Spring Boot Backend Validation

**Generated Files:**
- [ ] `test-spring-api/argocd/application.yaml` - Contains `app-type: springboot-backend`
- [ ] `test-spring-api/k8s/deployment.yaml` - Spring Boot-specific configuration
- [ ] `test-spring-api/k8s/configmap.yaml` - Spring Boot environment variables
- [ ] `test-spring-api/k8s/secret.yaml` - Full backend secrets
- [ ] Database manifests (if enabled)

**Configuration Validation:**
- [ ] Container port: 8080
- [ ] Health check path: "/actuator/health"
- [ ] Memory request: 512Mi (higher than frontend)
- [ ] CPU request: 250m (higher than frontend)
- [ ] Database init containers present (if database enabled)
- [ ] SPRING_* environment variables present
- [ ] Full secrets (JWT, DB, SMTP, OAuth)
- [ ] JVM configuration in JAVA_OPTS

## Expected Differences

### Resource Requirements

| Metric | React Frontend (Dev) | Spring Boot Backend (Dev) |
|--------|---------------------|---------------------------|
| Memory Request | 128Mi | 512Mi |
| Memory Limit | 256Mi | 1Gi |
| CPU Request | 50m | 250m |
| CPU Limit | 200m | 500m |

### Environment Variables

**React Frontend ConfigMap:**
```yaml
REACT_APP_API_URL: "http://localhost:8080"
REACT_APP_ENVIRONMENT: "dev"
REACT_APP_VERSION: "1.0.0"
REACT_APP_TITLE: "Test React App"
PUBLIC_URL: "/"
GENERATE_SOURCEMAP: "true"
```

**Spring Boot Backend ConfigMap:**
```yaml
SPRING_PROFILES_ACTIVE: "dev"
SERVER_PORT: "8080"
SPRING_APPLICATION_NAME: "test-spring-api"
SPRING_DATASOURCE_URL: "jdbc:postgresql://..."
SPRING_JPA_HIBERNATE_DDL_AUTO: "update"
JAVA_OPTS: "-Xms256m -Xmx512m ..."
```

### Health Checks

**React Frontend:**
```yaml
livenessProbe:
  httpGet:
    path: /
    port: 80
  initialDelaySeconds: 30
readinessProbe:
  httpGet:
    path: /
    port: 80
  initialDelaySeconds: 10
```

**Spring Boot Backend:**
```yaml
livenessProbe:
  httpGet:
    path: /actuator/health
    port: 8080
  initialDelaySeconds: 90
readinessProbe:
  httpGet:
    path: /actuator/health
    port: 8080
  initialDelaySeconds: 60
```

## Troubleshooting

### Common Issues

1. **Wrong Container Port**
   - React apps should use port 80, not 8080
   - Check deployment.yaml and service.yaml

2. **Incorrect Health Check**
   - React apps should use "/", not "/actuator/health"
   - Spring Boot apps should use "/actuator/health"

3. **Resource Limits Too High/Low**
   - React apps need less memory/CPU
   - Spring Boot apps need more memory for JVM

4. **Database Configuration**
   - React apps shouldn't have database configs
   - Spring Boot apps should have full database setup

### Validation Commands

```bash
# Check generated YAML syntax
find test-output -name "*.yaml" -exec yamllint {} \;

# Validate Kubernetes manifests
find test-output -path "*/k8s/*.yaml" -exec kubectl apply --dry-run=client -f {} \;

# Check ArgoCD application syntax
find test-output -path "*/argocd/*.yaml" -exec kubectl apply --dry-run=client -f {} \;
```

### Manual Verification

1. **Check Application Type Labels:**
   ```bash
   grep -r "app-type:" test-output/
   ```

2. **Verify Container Ports:**
   ```bash
   grep -r "containerPort:" test-output/
   ```

3. **Check Health Check Paths:**
   ```bash
   grep -r "path:" test-output/ | grep -E "(liveness|readiness)"
   ```

4. **Validate Resource Requests:**
   ```bash
   grep -A 5 -B 5 "requests:" test-output/
   ```

## Cleanup

```bash
# Remove test output
rm -rf test-output/

# Remove test applications from cluster (if deployed)
kubectl delete namespace test-react-app test-spring-api

# Remove ArgoCD applications (if created)
kubectl delete application test-react-app test-spring-api -n argocd
```

## Integration Testing

### End-to-End Test

1. Create test repository dispatch
2. Wait for GitHub Actions to complete
3. Verify manifests are generated and committed
4. Check ArgoCD application creation
5. Validate Kubernetes deployment
6. Test application health endpoints
7. Clean up test resources

### Automated Testing Script

```bash
#!/bin/bash
# test-application-types.sh

set -e

echo "Testing React Frontend template..."
python scripts/generate-manifests-cicd.py \
  --app-name "Test React" \
  --project-id "test-react" \
  --application-type "react-frontend" \
  --environment "dev" \
  --docker-image "nginx" \
  --docker-tag "alpine" \
  --output-dir "./test-react"

echo "Testing Spring Boot Backend template..."
python scripts/generate-manifests-cicd.py \
  --app-name "Test Spring" \
  --project-id "test-spring" \
  --application-type "springboot-backend" \
  --environment "dev" \
  --docker-image "openjdk" \
  --docker-tag "11-jre-slim" \
  --output-dir "./test-spring"

echo "Validating generated manifests..."
yamllint test-react/test-react/k8s/*.yaml
yamllint test-spring/test-spring/k8s/*.yaml

echo "Testing complete! Check test-react/ and test-spring/ directories."
```
