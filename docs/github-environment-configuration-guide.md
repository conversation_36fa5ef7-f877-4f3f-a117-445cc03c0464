# GitHub Environment Configuration Guide

This guide provides detailed instructions for creating and configuring GitHub Environments for the startup CI/CD pipeline with environment-based approvals.

## 🌍 Environment Overview

Our CI/CD pipeline uses three distinct GitHub Environments:

| Environment | Trigger | Approval | Protection Level |
|-------------|---------|----------|------------------|
| **dev** | Auto (push to main) | None | Low |
| **staging** | Manual | Optional | Medium |
| **production** | Manual | Required | High |

## 🔧 Environment Configuration Steps

### Step 1: Access Environment Settings

1. Navigate to your repository on GitHub
2. Click **Settings** tab
3. In the left sidebar, click **Environments**
4. You'll see the environment management page

### Step 2: Create DEV Environment

#### Basic Configuration
1. Click **New environment**
2. **Environment name**: `dev`
3. Click **Configure environment**

#### Protection Rules
**No protection rules needed** - <PERSON><PERSON> should allow automatic deployment

#### Environment Secrets
Add the following secrets specific to DEV environment:

```bash
# Database Configuration (DEV)
DB_HOST_SPRING_DEV=dev-db.example.com
DB_USER_SPRING_DEV=dev_user
DB_PASSWORD_SPRING_DEV=dev_secure_password
DB_NAME_SPRING_DEV=startup_app_dev
```

#### Environment Variables
Add these variables:

```bash
ENABLE_AUTO_DEPLOY=true
ENVIRONMENT_TYPE=development
LOG_LEVEL=debug
```

#### Deployment Branches
- **Selected branches**: Leave empty (allows all branches)
- This allows deployment from any branch for development flexibility

### Step 3: Create STAGING Environment

#### Basic Configuration
1. Click **New environment**
2. **Environment name**: `staging`
3. Click **Configure environment**

#### Protection Rules

**Required Reviewers** (Optional but recommended):
- ✅ **Required reviewers**: 1
- **Reviewers**: Add 1-2 team members who should approve staging deployments
- Example: QA lead, Product manager

**Wait Timer** (Optional):
- ⚠️ **Wait timer**: 0 minutes (no delay needed for staging)

**Deployment Branches**:
- ✅ **Selected branches**
- Add: `main` (only allow staging deployments from main branch)

#### Environment Secrets
Add staging-specific secrets:

```bash
# Database Configuration (STAGING)
DB_HOST_SPRING_STAGING=staging-db.example.com
DB_USER_SPRING_STAGING=staging_user
DB_PASSWORD_SPRING_STAGING=staging_secure_password
DB_NAME_SPRING_STAGING=startup_app_staging

# External Services (STAGING)
PAYMENT_API_KEY_STAGING=staging_payment_key
EMAIL_SERVICE_KEY_STAGING=staging_email_key
```

#### Environment Variables
```bash
ENVIRONMENT_TYPE=staging
LOG_LEVEL=info
ENABLE_MONITORING=true
```

### Step 4: Create PRODUCTION Environment

#### Basic Configuration
1. Click **New environment**
2. **Environment name**: `production`
3. Click **Configure environment**

#### Protection Rules

**Required Reviewers** (Strongly recommended):
- ✅ **Required reviewers**: 2
- **Reviewers**: Add senior team members
  - Technical lead
  - DevOps engineer
  - Product owner (if applicable)

**Wait Timer** (Recommended):
- ✅ **Wait timer**: 5 minutes
- This provides a safety window to cancel accidental deployments

**Deployment Branches**:
- ✅ **Selected branches**
- Add: `main` (only allow production deployments from main branch)

#### Environment Secrets
Add production-specific secrets:

```bash
# Database Configuration (PRODUCTION)
DB_HOST_SPRING_PROD=prod-db.example.com
DB_USER_SPRING_PROD=prod_user
DB_PASSWORD_SPRING_PROD=prod_secure_password
DB_NAME_SPRING_PROD=startup_app_production

# External Services (PRODUCTION)
PAYMENT_API_KEY_PROD=production_payment_key
EMAIL_SERVICE_KEY_PROD=production_email_key

# Monitoring and Alerting
DATADOG_API_KEY=production_datadog_key
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...
```

#### Environment Variables
```bash
ENVIRONMENT_TYPE=production
LOG_LEVEL=warn
ENABLE_MONITORING=true
ENABLE_ALERTING=true
REQUIRE_STAGING_VERIFICATION=true
```

## 🔐 Security Configuration

### Repository-Level Secrets

These secrets should be configured at the repository level (not environment-specific):

1. Navigate to **Settings** → **Secrets and variables** → **Actions**
2. Click **New repository secret**
3. Add the following:

```bash
# Core Infrastructure
GITOPS_TOKEN=ghp_xxxxxxxxxxxxxxxxxxxx
DIGITALOCEAN_ACCESS_TOKEN=dop_v1_xxxxxxxxxxxxxxxxxxxx

# Application Core Secrets
JWT_SECRET_SPRING=your_jwt_secret_here
ENABLE_DATABASE_SPRING=true
DB_PORT_SPRING=5432
DB_SSL_MODE_SPRING=require

# Email Configuration
SMTP_USER_SPRING=<EMAIL>
SMTP_PASS_SPRING=smtp_password_here

# OAuth Configuration
GOOGLE_CLIENT_ID_SPRING=google_client_id
GOOGLE_CLIENT_SECRET_SPRING=google_client_secret
```

### Environment Secret Inheritance

GitHub Environments inherit repository secrets but can override them:

- **Repository secrets**: Available to all environments
- **Environment secrets**: Override repository secrets for specific environment
- **Precedence**: Environment secrets > Repository secrets

## 🛡️ Advanced Protection Features

### For GitHub Team/Enterprise Plans

If you have GitHub Team or Enterprise plan, you can enable additional protection features:

#### Branch Protection Rules Integration
1. Navigate to **Settings** → **Branches**
2. Add rule for `main` branch:
   - ✅ Require a pull request before merging
   - ✅ Require review from CODEOWNERS
   - ✅ Require status checks to pass before merging

#### Environment-Specific Deployment Protection
```yaml
# In your workflow file
environment:
  name: production
  url: https://your-app.com  # Optional: deployment URL
```

### For GitHub Free/Pro Plans

Limited environment protection features available:
- Basic environment secrets
- Manual deployment triggers
- Simple approval simulation through workflow logic

## 📋 Environment Validation Checklist

### DEV Environment ✅
- [ ] Environment name: `dev`
- [ ] No protection rules configured
- [ ] DEV-specific database secrets added
- [ ] `ENABLE_AUTO_DEPLOY=true` variable set
- [ ] Auto-deployment workflow can access environment

### STAGING Environment ✅
- [ ] Environment name: `staging`
- [ ] Optional reviewers configured (1-2 people)
- [ ] Deployment restricted to `main` branch
- [ ] STAGING-specific secrets added
- [ ] Manual deployment workflow configured

### PRODUCTION Environment ✅
- [ ] Environment name: `production`
- [ ] Required reviewers configured (2+ people)
- [ ] Wait timer configured (5 minutes recommended)
- [ ] Deployment restricted to `main` branch
- [ ] PRODUCTION-specific secrets added
- [ ] All safety checks enabled

## 🔍 Testing Environment Configuration

### Test DEV Environment
```bash
# This should work automatically on push to main
git push origin main
# Check Actions tab for auto-triggered workflow
```

### Test STAGING Environment
```bash
# Navigate to Actions → "Deploy to STAGING Environment"
# Click "Run workflow" and fill parameters
# Verify approval process if configured
```

### Test PRODUCTION Environment
```bash
# Navigate to Actions → "Deploy to PRODUCTION Environment"
# Click "Run workflow" and fill parameters
# Go through all approval gates
# Verify safety delays and checks
```

## 🚨 Troubleshooting

### Common Issues

#### Environment Not Found
```
Error: Environment 'staging' not found
```
**Solution**: Verify environment name matches exactly (case-sensitive)

#### Permission Denied
```
Error: Resource not accessible by integration
```
**Solution**: Check if `GITOPS_TOKEN` has sufficient permissions

#### Reviewer Not Available
```
Waiting for required reviewers
```
**Solution**: 
- Check if reviewers have repository access
- Verify reviewers are not the same as the person triggering deployment
- Consider adding backup reviewers

#### Secret Not Available
```
Error: Secret 'DB_PASSWORD_SPRING_PROD' not found
```
**Solution**:
- Verify secret is configured in correct environment
- Check secret name spelling
- Ensure environment has access to repository secrets

## 📈 Best Practices

### Environment Naming
- Use lowercase names: `dev`, `staging`, `production`
- Be consistent across all repositories
- Avoid special characters or spaces

### Secret Management
- Use different secrets for each environment
- Rotate secrets regularly
- Use least-privilege access principles
- Never commit secrets to code

### Approval Configuration
- **DEV**: No approvals (fast iteration)
- **STAGING**: 1 reviewer (quality gate)
- **PRODUCTION**: 2+ reviewers (safety gate)

### Deployment Timing
- **DEV**: Anytime (auto-deployment)
- **STAGING**: Business hours preferred
- **PRODUCTION**: Maintenance windows only

## 🔗 Related Documentation

- [Environment Secrets Configuration](./environment-secrets-configuration.md)
- [Startup CI/CD Setup Guide](./startup-cicd-environment-setup.md)
- [Testing Guide](./startup-cicd-testing-guide.md)
- [GitHub Environments Documentation](https://docs.github.com/en/actions/deployment/targeting-different-environments/using-environments-for-deployment)

## 📞 Support

If you encounter issues:

1. Check the troubleshooting section above
2. Review GitHub Actions logs for detailed error messages
3. Verify all prerequisites are met
4. Consult GitHub documentation for environment-specific features
5. Consider reaching out to your DevOps team or GitHub support
