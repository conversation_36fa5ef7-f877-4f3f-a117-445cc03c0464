# 🚀 Self-Service GitOps Application Deployment

This repository provides a fully automated self-service system for deploying applications using GitOps principles with ArgoCD. Teams can deploy applications by simply creating a GitHub issue with the required configuration.

## 📋 Quick Start

1. **Create a Deployment Request**: [Create New Issue](../../issues/new/choose) → Select "🚀 Application Deployment Request"
2. **Fill the Form**: Complete all required fields in the issue template
3. **Submit**: The automation will generate all necessary manifests and commit them to the repository
4. **Deploy**: Use the generated ArgoCD manifests to deploy your application

## 🎯 Features

### ✅ What's Automated
- **Complete Project Structure**: Generates `{project-name}/argocd/` and `{project-name}/k8s/` directories
- **ArgoCD Integration**: Application and Project manifests ready for deployment
- **Kubernetes Manifests**: Namespace, Deployment, Service, ConfigMap, Secret templates
- **Optional Components**: PostgreSQL database, Ingress, Persistent Volumes
- **Validation**: Comprehensive input validation with helpful error messages
- **Git Integration**: Automatic commit and push of generated files

### 🔧 Supported Application Types
- **Web Applications**: Frontend apps with ingress configuration
- **APIs**: Backend services with health checks and database integration
- **Microservices**: Lightweight services with minimal resource requirements
- **Workers**: Background processing applications
- **Databases**: Specialized database deployments

### 🌍 Environment Support
- **Development**: Lower resource limits, relaxed security
- **Staging**: Production-like configuration for testing
- **Production**: High availability, security-focused configuration

## 📝 Issue Template Fields

### Required Fields
| Field | Description | Example |
|-------|-------------|---------|
| Application Name | Human-readable name | `My Awesome App` |
| Project Identifier | Unique ID (lowercase, alphanumeric, hyphens) | `my-awesome-app` |
| Container Image | Full image path with tag | `docker.io/myorg/my-app:v1.0.0` |
| Environment | Target environment | `dev`, `staging`, `prod` |
| Replica Count | Number of instances | `1`, `3`, `5` |
| CPU Request/Limit | CPU resources | `100m`, `500m`, `1` |
| Memory Request/Limit | Memory resources | `256Mi`, `512Mi`, `1Gi` |
| Container Port | Application port | `8080`, `3000`, `8000` |
| Service Type | Kubernetes service type | `ClusterIP`, `NodePort`, `LoadBalancer` |
| Application Type | Type of application | `web-app`, `api`, `microservice` |

### Optional Fields
| Field | Description | When to Use |
|-------|-------------|-------------|
| Kubernetes Namespace | Custom namespace | Different from project ID |
| NodePort | Specific port (30000-32767) | NodePort service type |
| Ingress Configuration | External access | Public-facing applications |
| Environment Variables | Non-sensitive config | Application configuration |
| Secret Keys | Sensitive data keys | API keys, passwords |
| PostgreSQL Database | Database deployment | Apps requiring database |
| Persistent Volume | Data storage | Stateful applications |
| Health Check Path | Health endpoint | `/health`, `/api/health` |

## 🏗️ Generated Structure

```
{project-name}/
├── argocd/
│   ├── application.yaml    # ArgoCD Application manifest
│   └── project.yaml        # ArgoCD Project manifest
└── k8s/
    ├── namespace.yaml      # Kubernetes namespace
    ├── deployment.yaml     # Application deployment
    ├── service.yaml        # Service configuration
    ├── configmap.yaml      # Environment variables (if specified)
    ├── secret.yaml         # Secret templates (if specified)
    ├── ingress.yaml        # Ingress configuration (if enabled)
    ├── postgres-*.yaml     # PostgreSQL manifests (if enabled)
    └── pvc.yaml           # Persistent volume (if enabled)
```

## 🚀 Deployment Process

### 1. After Issue Creation
The automation will:
- Validate all input parameters
- Check for existing projects
- Generate complete manifest set
- Commit files to repository
- Close issue with deployment instructions

### 2. Manual Steps Required

#### Update Secrets (Critical!)
```bash
# Edit the secret file
vim {project-name}/k8s/secret.yaml

# Replace PLACEHOLDER values with actual base64-encoded secrets
echo -n "your-secret-value" | base64
```

#### Deploy with ArgoCD
```bash
# Apply ArgoCD manifests
kubectl apply -f {project-name}/argocd/project.yaml
kubectl apply -f {project-name}/argocd/application.yaml

# Monitor deployment
kubectl get applications -n argocd
kubectl get pods -n {project-name}
```

### 3. Verification
```bash
# Check application status
kubectl get all -n {project-name}

# View ArgoCD application
kubectl get application {project-name} -n argocd -o yaml

# Test application (if NodePort)
minikube ip  # Get cluster IP
curl http://{cluster-ip}:{node-port}
```

## 🔍 Validation Rules

### Project Identifier
- 3-63 characters long
- Lowercase letters, numbers, hyphens only
- Cannot start/end with hyphen
- No consecutive hyphens
- Cannot use reserved names (`default`, `kube-system`, etc.)

### Resource Formats
- **CPU**: `100m`, `0.5`, `1` (millicores or cores)
- **Memory**: `256Mi`, `1Gi`, `2Ti` (with units)
- **Storage**: `1Gi`, `10Gi`, `1Ti` (with units)

### Ports
- **Container Port**: 1-65535
- **NodePort**: 30000-32767

### Environment Variables
- Format: `KEY=value` (one per line)
- Keys: Uppercase letters, numbers, underscores only

## 🛠️ Troubleshooting

### Common Issues

#### Validation Errors
```
❌ Project Identifier must be lowercase alphanumeric with hyphens only
```
**Solution**: Use only lowercase letters, numbers, and hyphens in project identifier.

#### Resource Format Errors
```
❌ Memory Request must be in format like '256Mi', '1Gi', '2Ti'
```
**Solution**: Include units (Mi, Gi, Ti) with memory values.

#### Missing Required Fields
```
❌ Container Image is required
```
**Solution**: Fill all required fields marked with red asterisk (*).

### Deployment Issues

#### ArgoCD Application Not Syncing
```bash
# Check application status
kubectl describe application {project-name} -n argocd

# Check for sync errors
kubectl get application {project-name} -n argocd -o yaml
```

#### Pods Not Starting
```bash
# Check pod status
kubectl get pods -n {project-name}

# View pod logs
kubectl logs deployment/{project-name} -n {project-name}

# Check events
kubectl get events -n {project-name} --sort-by='.lastTimestamp'
```

#### Secret Issues
```bash
# Verify secret exists
kubectl get secret {project-name}-secrets -n {project-name}

# Check secret data
kubectl get secret {project-name}-secrets -n {project-name} -o yaml
```

### Getting Help

1. **Check Workflow Logs**: [Actions Tab](../../actions) for detailed error information
2. **Review Generated Files**: Inspect the generated manifests for issues
3. **Validate Manually**: Use `scripts/validate-deployment.ps1` for local validation
4. **Create Support Issue**: [Create Issue](../../issues/new) with label `support`

## 📚 Advanced Usage

### Custom Configurations

#### Multiple Environments
Create separate issues for each environment:
- `my-app-dev` (development)
- `my-app-staging` (staging)  
- `my-app-prod` (production)

#### Database Integration
Enable PostgreSQL database for applications requiring persistent data storage:
- Check "Enable PostgreSQL Database"
- Specify database name and storage size
- Update connection strings in application

#### Ingress Configuration
For public-facing applications:
- Check "Enable Ingress"
- Provide domain name
- Configure DNS to point to cluster

### Integration with CI/CD

#### Automatic Image Updates
```yaml
# In your CI pipeline
- name: Update Deployment Image
  run: |
    sed -i 's|image: .*|image: ${{ env.NEW_IMAGE }}|' \
      {project-name}/k8s/deployment.yaml
    git commit -am "Update image to ${{ env.NEW_IMAGE }}"
    git push
```

#### Environment Promotion
```bash
# Copy staging config to production
cp -r my-app-staging/ my-app-prod/
# Update environment-specific values
sed -i 's/staging/prod/g' my-app-prod/k8s/*.yaml
```

## 🔐 Security Considerations

### Secret Management
- **Never commit actual secrets** to the repository
- Use external secret management (Vault, AWS Secrets Manager)
- Rotate secrets regularly
- Use least-privilege access

### Network Security
- Use `ClusterIP` services for internal communication
- Configure ingress with TLS termination
- Implement network policies for isolation

### Resource Limits
- Always set resource limits to prevent resource exhaustion
- Use appropriate security contexts
- Enable Pod Security Standards

## 📈 Best Practices

### Resource Planning
- Start with conservative resource requests
- Monitor actual usage and adjust
- Use horizontal pod autoscaling for variable loads

### Naming Conventions
- Use descriptive, consistent project identifiers
- Include environment in names when needed
- Follow organization naming standards

### Configuration Management
- Use ConfigMaps for non-sensitive configuration
- Externalize environment-specific values
- Version configuration changes

### Monitoring and Observability
- Implement health checks for all applications
- Use structured logging
- Set up monitoring and alerting

## 🤝 Contributing

### Improving the System
1. **Template Updates**: Modify templates in `templates/` directory
2. **Validation Rules**: Update `scripts/validate-deployment.ps1`
3. **Workflow Enhancements**: Improve `.github/workflows/generate-app-manifests.yaml`
4. **Documentation**: Keep this guide updated with changes

### Testing Changes
```bash
# Validate templates locally
./scripts/validate-deployment.ps1 -IssueBody "$(cat test-issue.txt)" -Detailed

# Test manifest generation
./scripts/generate-manifests.ps1 -IssueBody "$(cat test-issue.txt)" -DryRun
```

## 📖 Examples

### Example 1: Simple Web Application
```yaml
Application Name: Frontend Dashboard
Project Identifier: frontend-dashboard
Container Image: nginx:1.21
Environment: dev
Replicas: 2
CPU Request: 100m
CPU Limit: 200m
Memory Request: 128Mi
Memory Limit: 256Mi
Container Port: 80
Service Type: NodePort
Application Type: web-app
Enable Ingress: ✓
Ingress Host: dashboard.example.com
```

### Example 2: API with Database
```yaml
Application Name: User API
Project Identifier: user-api
Container Image: myorg/user-api:v2.1.0
Environment: prod
Replicas: 3
CPU Request: 200m
CPU Limit: 500m
Memory Request: 512Mi
Memory Limit: 1Gi
Container Port: 8080
Service Type: ClusterIP
Application Type: api
Enable PostgreSQL Database: ✓
Database Name: userdb
Storage Size: 10Gi
Health Check Path: /api/health
Environment Variables:
LOG_LEVEL=info
API_VERSION=v2
Secret Keys:
JWT_SECRET
DB_PASSWORD
API_KEY
```

### Example 3: Microservice with PVC
```yaml
Application Name: File Processor
Project Identifier: file-processor
Container Image: myorg/processor:latest
Environment: staging
Replicas: 1
CPU Request: 500m
CPU Limit: 1
Memory Request: 1Gi
Memory Limit: 2Gi
Container Port: 9000
Service Type: ClusterIP
Application Type: microservice
Enable Persistent Volume: ✓
PVC Size: 5Gi
PVC Mount Path: /app/data
```

---

**Need Help?** Create an issue with the `support` label or check the [troubleshooting section](#-troubleshooting) above.
