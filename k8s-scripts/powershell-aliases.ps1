# Backend switching aliases for PowerShell
# Add these to your PowerShell profile ($PROFILE)

# Get the script directory (adjust path as needed)
$SCRIPT_DIR = "$env:USERPROFILE\Softwares\gitops-argocd-apps\k8s-scripts"

# Backend switching functions
function Switch-Spring { & "$SCRIPT_DIR\switch-backend.ps1" -Backend spring }
function Switch-Django { & "$SCRIPT_DIR\switch-backend.ps1" -Backend django }
function Switch-Nest { & "$SCRIPT_DIR\switch-backend.ps1" -Backend nest }

# Status and monitoring functions
function Get-CurrentBackend { 
    kubectl get configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev -o jsonpath='{.data.runtime-config\.json}' | jq .currentBackend 
}

function Get-BackendStatus { 
    kubectl get configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev -o jsonpath='{.data.runtime-config\.json}' | jq . 
}

function Invoke-HealthCheck { & "$SCRIPT_DIR\backend-health.ps1" }

function Get-FrontendLogs { 
    kubectl logs -f deployment/ai-react-frontend -n ai-react-frontend-dev 
}

function Get-FrontendPods { 
    kubectl get pods -n ai-react-frontend-dev -l app=ai-react-frontend 
}

function Get-FrontendConfig { 
    try {
        $response = Invoke-WebRequest -Uri "http://*************:3000/api/config" -Method GET
        $response.Content | jq .
    } catch {
        Write-Host "❌ Failed to get frontend config: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Create aliases
Set-Alias -Name switch-spring -Value Switch-Spring
Set-Alias -Name switch-django -Value Switch-Django
Set-Alias -Name switch-nest -Value Switch-Nest
Set-Alias -Name current-backend -Value Get-CurrentBackend
Set-Alias -Name backend-status -Value Get-BackendStatus
Set-Alias -Name health-check -Value Invoke-HealthCheck
Set-Alias -Name frontend-logs -Value Get-FrontendLogs
Set-Alias -Name frontend-pods -Value Get-FrontendPods
Set-Alias -Name frontend-config -Value Get-FrontendConfig

Write-Host "✅ Backend switching aliases loaded!" -ForegroundColor Green
Write-Host "Available commands:" -ForegroundColor Cyan
Write-Host "  switch-spring    - Switch to Spring Boot backend" -ForegroundColor White
Write-Host "  switch-django    - Switch to Django backend" -ForegroundColor White
Write-Host "  switch-nest      - Switch to NestJS backend" -ForegroundColor White
Write-Host "  current-backend  - Show current backend" -ForegroundColor White
Write-Host "  backend-status   - Show full backend configuration" -ForegroundColor White
Write-Host "  health-check     - Check health of all backends" -ForegroundColor White
Write-Host "  frontend-logs    - Monitor frontend logs" -ForegroundColor White
Write-Host "  frontend-pods    - Show frontend pods" -ForegroundColor White
Write-Host "  frontend-config  - Test frontend config API" -ForegroundColor White
