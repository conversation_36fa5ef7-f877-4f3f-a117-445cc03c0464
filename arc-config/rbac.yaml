---
# ServiceAccount for GitOps runners
apiVersion: v1
kind: ServiceAccount
metadata:
  name: gitops-runner-sa
  namespace: ch-arc-runner-system
  labels:
    app: chidhagni-organisation-runner
    component: service-account
  annotations:
    description: "Service account for GitOps ArgoCD automation runners on DOKS"

---
# ClusterRole with comprehensive permissions for GitOps operations
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: gitops-runner-role
  labels:
    app: chidhagni-organisation-runner
    component: cluster-role
  annotations:
    description: "Cluster role for GitOps ArgoCD automation with comprehensive permissions on DOKS"
rules:
# Core Kubernetes resources
- apiGroups: [""]
  resources: 
    - namespaces
    - pods
    - pods/log
    - pods/status
    - services
    - endpoints
    - configmaps
    - secrets
    - persistentvolumes
    - persistentvolumeclaims
    - serviceaccounts
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

# Apps resources
- apiGroups: ["apps"]
  resources:
    - deployments
    - replicasets
    - statefulsets
    - daemonsets
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

# Extensions resources
- apiGroups: ["extensions"]
  resources:
    - deployments
    - replicasets
    - ingresses
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

# Networking resources
- apiGroups: ["networking.k8s.io"]
  resources:
    - ingresses
    - networkpolicies
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

# RBAC resources (limited to avoid privilege escalation)
- apiGroups: ["rbac.authorization.k8s.io"]
  resources:
    - roles
    - rolebindings
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

# ArgoCD CRDs - Full access for GitOps automation
- apiGroups: ["argoproj.io"]
  resources:
    - applications
    - applicationsets
    - appprojects
    - workflows
    - workflowtemplates
    - cronworkflows
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

# Metrics and monitoring
- apiGroups: ["metrics.k8s.io"]
  resources:
    - pods
    - nodes
  verbs: ["get", "list"]

# Autoscaling
- apiGroups: ["autoscaling"]
  resources:
    - horizontalpodautoscalers
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

# Storage
- apiGroups: ["storage.k8s.io"]
  resources:
    - storageclasses
    - volumeattachments
  verbs: ["get", "list", "watch"]

# Events for debugging
- apiGroups: [""]
  resources:
    - events
  verbs: ["get", "list", "watch"]

# Custom resources for operators
- apiGroups: ["apiextensions.k8s.io"]
  resources:
    - customresourcedefinitions
  verbs: ["get", "list", "watch"]

# Batch jobs
- apiGroups: ["batch"]
  resources:
    - jobs
    - cronjobs
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

# Policy resources
- apiGroups: ["policy"]
  resources:
    - poddisruptionbudgets
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

# Node access for cluster information
- apiGroups: [""]
  resources:
    - nodes
  verbs: ["get", "list", "watch"]

---
# ClusterRoleBinding to bind the service account to the cluster role
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: gitops-runner-binding
  labels:
    app: chidhagni-organisation-runner
    component: cluster-role-binding
  annotations:
    description: "Cluster role binding for GitOps ArgoCD automation runners on DOKS"
subjects:
- kind: ServiceAccount
  name: gitops-runner-sa
  namespace: ch-arc-runner-system
roleRef:
  kind: ClusterRole
  name: gitops-runner-role
  apiGroup: rbac.authorization.k8s.io

---
# Additional Role for ch-arc-runner-system namespace operations
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: gitops-runner-arc-role
  namespace: ch-arc-runner-system
  labels:
    app: chidhagni-organisation-runner
    component: namespace-role
rules:
# Full access to ch-arc-runner-system namespace for runner management
- apiGroups: [""]
  resources: ["*"]
  verbs: ["*"]
- apiGroups: ["apps"]
  resources: ["*"]
  verbs: ["*"]
- apiGroups: ["actions.github.com"]
  resources: ["*"]
  verbs: ["*"]

---
# RoleBinding for ch-arc-runner-system namespace
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: gitops-runner-arc-binding
  namespace: ch-arc-runner-system
  labels:
    app: chidhagni-organisation-runner
    component: namespace-role-binding
subjects:
- kind: ServiceAccount
  name: gitops-runner-sa
  namespace: ch-arc-runner-system
roleRef:
  kind: Role
  name: gitops-runner-arc-role
  apiGroup: rbac.authorization.k8s.io
