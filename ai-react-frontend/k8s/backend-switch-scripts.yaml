apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-react-frontend-switch-scripts
  namespace: ai-react-frontend-dev
  labels:
    app: ai-react-frontend
    component: switch-scripts
data:
  switch-to-spring.sh: |
    #!/bin/bash
    set -e
    echo "🔄 Switching to Spring Boot backend..."
    
    kubectl patch configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev --type merge -p '{
      "data": {
        "runtime-config.json": "{\"currentBackend\":\"spring\",\"backendUrl\":\"http://************:8080\",\"environment\":\"dev\",\"serviceName\":\"ai-spring-backend-service\",\"namespace\":\"ai-spring-backend-dev\",\"apiVersion\":\"v1\",\"lastUpdated\":\"'$(date -u +%Y-%m-%dT%H:%M:%SZ)'\"}"
      }
    }'
    
    kubectl rollout restart deployment ai-react-frontend -n ai-react-frontend-dev
    kubectl rollout status deployment ai-react-frontend -n ai-react-frontend-dev
    echo "✅ Switched to Spring Boot backend (************:8080)"

  switch-to-django.sh: |
    #!/bin/bash
    set -e
    echo "🔄 Switching to Django backend..."
    
    kubectl patch configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev --type merge -p '{
      "data": {
        "runtime-config.json": "{\"currentBackend\":\"django\",\"backendUrl\":\"http://152.42.157.69:8000\",\"environment\":\"dev\",\"serviceName\":\"ai-django-backend-service\",\"namespace\":\"ai-django-backend-dev\",\"apiVersion\":\"v1\",\"lastUpdated\":\"'$(date -u +%Y-%m-%dT%H:%M:%SZ)'\"}"
      }
    }'
    
    kubectl rollout restart deployment ai-react-frontend -n ai-react-frontend-dev
    kubectl rollout status deployment ai-react-frontend -n ai-react-frontend-dev
    echo "✅ Switched to Django backend (152.42.157.69:8000)"

  switch-to-nest.sh: |
    #!/bin/bash
    set -e
    echo "🔄 Switching to NestJS backend..."
    
    # Check if NestJS backend is ready
    if ! kubectl get service ai-nest-backend-service -n ai-nest-backend-dev >/dev/null 2>&1; then
      echo "❌ NestJS backend service not found. Please deploy NestJS backend first."
      exit 1
    fi
    
    kubectl patch configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev --type merge -p '{
      "data": {
        "runtime-config.json": "{\"currentBackend\":\"nest\",\"backendUrl\":\"http://139.59.53.144:3000\",\"environment\":\"dev\",\"serviceName\":\"ai-nest-backend-service\",\"namespace\":\"ai-nest-backend-dev\",\"apiVersion\":\"v1\",\"lastUpdated\":\"'$(date -u +%Y-%m-%dT%H:%M:%SZ)'\"}"
      }
    }'
    
    kubectl rollout restart deployment ai-react-frontend -n ai-react-frontend-dev
    kubectl rollout status deployment ai-react-frontend -n ai-react-frontend-dev
    echo "✅ Switched to NestJS backend (139.59.53.144:3000)"

  get-current-backend.sh: |
    #!/bin/bash
    echo "📋 Current Backend Configuration:"
    kubectl get configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev -o jsonpath='{.data.runtime-config\.json}' | jq .
    
    echo -e "\n🔍 Backend Health Status:"
    CURRENT_URL=$(kubectl get configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev -o jsonpath='{.data.runtime-config\.json}' | jq -r .backendUrl)
    BACKEND_TYPE=$(kubectl get configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev -o jsonpath='{.data.runtime-config\.json}' | jq -r .currentBackend)
    
    case $BACKEND_TYPE in
      "spring")
        curl -f $CURRENT_URL/actuator/health && echo "✅ Spring Boot is healthy" || echo "❌ Spring Boot is not responding"
        ;;
      "django")
        curl -f $CURRENT_URL/health && echo "✅ Django is healthy" || echo "❌ Django is not responding"
        ;;
      "nest")
        curl -f $CURRENT_URL/health && echo "✅ NestJS is healthy" || echo "❌ NestJS is not responding"
        ;;
    esac
