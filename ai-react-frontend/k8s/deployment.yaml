apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-react-frontend
  namespace: ai-react-frontend-dev
  labels:
    app: ai-react-frontend
    version: v1.0.0
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-react-frontend
      app.kubernetes.io/managed-by: argocd
      app.kubernetes.io/name: ai-react-frontend
      app.kubernetes.io/version: "1.0.0"
  template:
    metadata:
      labels:
        app: ai-react-frontend
        app.kubernetes.io/managed-by: argocd
        app.kubernetes.io/name: ai-react-frontend
        app.kubernetes.io/version: "1.0.0"
    spec:
      containers:
        - name: ai-react-frontend
          image: registry.digitalocean.com/doks-registry/ai-react-frontend:latest
          ports:
            - containerPort: 3000
          env:
            - name: VITE_APP_ENV
              value: "dev"
            - name: VITE_APP_SERVICE_NAME
              value: "ai-spring-backend-service"
            - name: VITE_APP_BACKEND_NAMESPACE
              value: "ai-spring-backend-dev"
          volumeMounts:
            # Mount env-config.js to the public folder
            - name: env-config-volume
              mountPath: /usr/share/nginx/html/env-config.js
              subPath: env-config.js
              readOnly: true
            # Mount nginx configuration
            - name: nginx-config-volume
              mountPath: /etc/nginx/conf.d
              readOnly: true
          resources:
            requests:
              memory: "128Mi"
              cpu: "100m"
            limits:
              memory: "256Mi"
              cpu: "200m"
      volumes:
        # Volume for runtime environment configuration
        - name: env-config-volume
          configMap:
            name: ai-react-frontend-env
        # Volume for nginx configuration
        - name: nginx-config-volume
          configMap:
            name: ai-react-frontend-nginx-config
      restartPolicy: Always
