apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-react-frontend-env
  namespace: ai-react-frontend-dev
  labels:
    app: ai-react-frontend
    component: environment-config
    managed-by: argocd
    environment: dev
  annotations:
    argocd.argoproj.io/sync-wave: "1"
    description: "Runtime environment configuration for dev environment - all backends deployed"
data:
  env-config.js: |
    window._env_ = {
      // Current active backend (change this to switch backends)
      REACT_APP_BACKEND_URL: "http://*************:3000",  // Nest Backend (active)
      REACT_APP_CURRENT_BACKEND: "nest",

      // Environment configuration
      REACT_APP_ENVIRONMENT: "dev",
      REACT_APP_API_VERSION: "v1",
      REACT_APP_LAST_UPDATED: "2025-07-26T12:15:00Z",

      // Service configuration for dev environment
      REACT_APP_SERVICE_NAME: "ai-nest-backend-service",
      REACT_APP_BACKEND_NAMESPACE: "ai-nest-backend-dev",

      // OAuth configuration
      REACT_APP_GOOGLE_OAUTH_URL: "http://*************:3000/oauth2/authorize/google?redirect_uri=http://**************:3000/oauth2/redirect",

      // Feature flags
      REACT_APP_USE_RUNTIME_CONFIG: "true",
      REACT_APP_DEBUG_MODE: "true"
    };

    // Available backend configurations for dev environment:
    //
    // Spring Boot Backend (COMMENTED OUT):
    // REACT_APP_BACKEND_URL: "http://139.59.50.43:8080"
    // REACT_APP_CURRENT_BACKEND: "spring"
    // REACT_APP_SERVICE_NAME: "ai-spring-backend-service"
    // REACT_APP_BACKEND_NAMESPACE: "ai-spring-backend-dev"
    //
    // Django Backend (COMMENTED OUT):
    // REACT_APP_BACKEND_URL: "http://152.42.157.69:8000"
    // REACT_APP_CURRENT_BACKEND: "django"
    // REACT_APP_SERVICE_NAME: "ai-django-backend-service"
    // REACT_APP_BACKEND_NAMESPACE: "ai-django-backend-dev"
    //

