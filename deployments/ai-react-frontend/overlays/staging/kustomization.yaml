apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: PLACEHOLDER_PROJECT_ID-staging

resources:
- ../../base
- secret.yaml

components:
- ../../components/common-labels
- ../../components/database-init

labels:
- pairs:
    app: PLACEHOLDER_PROJECT_ID
    app.kubernetes.io/name: PLACEHOLDER_PROJECT_ID
    app.kubernetes.io/part-of: PLACEHOLDER_APP_NAME
    app.kubernetes.io/component: PLACEHOLDER_APPLICATION_TYPE
    app.kubernetes.io/version: PLACEHOLDER_COMMIT_SHA
    app.kubernetes.io/managed-by: argocd
    environment: staging
    source.repo: PLACEHOLDER_SOURCE_REPO_LABEL
    source.branch: PLACEHOLDER_SOURCE_BRANCH_LABEL

patches:
- target:
    kind: Deployment
    name: PLACEHOLDER_PROJECT_ID
  patch: |-
    - op: replace
      path: /spec/replicas
      value: 2
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/cpu
      value: "500m"
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/memory
      value: "512Mi"
    - op: replace
      path: /spec/template/spec/containers/0/resources/limits/cpu
      value: "1000m"
    - op: replace
      path: /spec/template/spec/containers/0/resources/limits/memory
      value: "1Gi"

- target:
    kind: ConfigMap
    name: PLACEHOLDER_PROJECT_ID-config
  patch: |-
    - op: replace
      path: /data/NODE_ENV
      value: "staging"
    - op: replace
      path: /data/SPRING_PROFILES_ACTIVE
      value: "staging"
    - op: replace
      path: /data/DEBUG
      value: "False"
    - op: replace
      path: /data/GENERATE_SOURCEMAP
      value: "false"

# Database init container environment-specific patch is handled by the database-init component

patchesStrategicMerge:
- patch-image.yaml

namePrefix: ""
nameSuffix: "-staging"
