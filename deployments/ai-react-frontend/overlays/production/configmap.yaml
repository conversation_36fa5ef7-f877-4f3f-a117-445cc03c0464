apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-react-frontend-config
  labels:
    app: ai-react-frontend
    app.kubernetes.io/name: ai-react-frontend
    app.kubernetes.io/component: config
    app.kubernetes.io/part-of: AI React Frontend
    app.kubernetes.io/version: "405e961c"
    app.kubernetes.io/managed-by: argocd
data:
  # Application Configuration
  APP_NAME: "AI React Frontend"
  PROJECT_ID: "ai-react-frontend"
  APPLICATION_TYPE: "react-frontend"
  SOURCE_REPO: "ChidhagniConsulting/ai-react-frontend"
  SOURCE_BRANCH: "main"
  COMMIT_SHA: "405e961c"
  NODE_ENV: "production"
  PORT: "3000"
  
  # Database Configuration (DigitalOcean PostgreSQL)
  # Database connection details are configured via environment variables in deployment from secrets
  
  # Application URLs
  APP_URL: "http://localhost:3000"
  API_URL: "http://localhost:3000/api"
  
  # Common Backend Configuration
  SERVER_PORT: "3000"

  

  

  

  
  # React Frontend Configuration
  REACT_APP_API_URL: "http://localhost:3000/api"
  REACT_APP_ENVIRONMENT: "production"
  REACT_APP_VERSION: "1.0.0"
  REACT_APP_TITLE: "My App"
  PUBLIC_URL: "/"
  GENERATE_SOURCEMAP: "false"
  
