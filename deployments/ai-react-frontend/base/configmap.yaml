apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-react-frontend-config
  labels:
    app: ai-react-frontend
    app.kubernetes.io/name: ai-react-frontend
    app.kubernetes.io/component: config
    app.kubernetes.io/part-of: AI React Frontend
    app.kubernetes.io/version: 53a0a299
    app.kubernetes.io/managed-by: argocd
data:
  # Application Configuration
  APP_NAME: "AI React Frontend"
  PROJECT_ID: "ai-react-frontend"
  APPLICATION_TYPE: "react-frontend"
  SOURCE_REPO: "ChidhagniConsulting/ai-react-frontend"
  SOURCE_BRANCH: "main"
  COMMIT_SHA: "53a0a299"
  NODE_ENV: "dev"
  PORT: "8080"
  
  # Database Configuration (DigitalOcean PostgreSQL)
  # These values will be replaced by GitHub Actions workflow from dispatch payload
  DB_HOST: "PLACEHOLDER_DB_HOST"
  DB_PORT: "PLACEHOLDER_DB_PORT"
  DB_NAME: "PLACEHOLDER_DB_NAME"
  DB_USER: "PLACEHOLDER_DB_USER"
  DB_SSL_MODE: "require"
  
  # Application URLs
  APP_URL: "http://localhost:8080"
  API_URL: "http://localhost:8080/api"
  
  # Common Backend Configuration
  SPRING_PROFILES_ACTIVE: "dev"
  SERVER_PORT: "8080"
  SPRING_APPLICATION_NAME: "app"

  # Spring Boot Database Configuration (Managed DigitalOcean Database)
  SPRING_DATASOURCE_URL: "*********************************************************************************************"
  SPRING_DATASOURCE_DRIVER_CLASS_NAME: "org.postgresql.Driver"
  SPRING_DATASOURCE_USERNAME: "PLACEHOLDER_DB_USER"
  SPRING_JPA_HIBERNATE_DDL_AUTO: "update"
  SPRING_JPA_SHOW_SQL: "true"
  SPRING_JPA_PROPERTIES_HIBERNATE_DIALECT: "org.hibernate.dialect.PostgreSQLDialect"
  SPRING_JPA_PROPERTIES_HIBERNATE_FORMAT_SQL: "true"

  # Spring Boot Management & Actuator
  MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE: "health,info,metrics,prometheus"
  MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS: "when-authorized"
  MANAGEMENT_HEALTH_PROBES_ENABLED: "true"
  
  # JVM Configuration
  JAVA_OPTS: "-Xms256m -Xmx512m -XX:+UseG1GC -XX:+UseContainerSupport"
  
  # Django Configuration
  DJANGO_SETTINGS_MODULE: "app.settings.dev"
  DEBUG: "False"
  ALLOWED_HOSTS: "localhost,127.0.0.1"
  
  # Django Static Files
  STATIC_URL: "/static/"
  STATIC_ROOT: "/app/staticfiles/"
  MEDIA_URL: "/media/"
  MEDIA_ROOT: "/app/media/"
  
  # Django CORS Configuration
  CORS_ALLOWED_ORIGINS: "http://localhost:3000,http://127.0.0.1:3000"
  CORS_ALLOW_CREDENTIALS: "true"
  
  # NestJS Configuration
  APP_PORT: "8080"
  
  # React Frontend Configuration
  REACT_APP_API_URL: "http://localhost:8080/api"
  REACT_APP_ENVIRONMENT: "dev"
  REACT_APP_VERSION: "1.0.0"
  REACT_APP_TITLE: "My App"
  PUBLIC_URL: "/"
  GENERATE_SOURCEMAP: "true"
