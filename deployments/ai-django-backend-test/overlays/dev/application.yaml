apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ai-django-backend-test-dev
  namespace: argocd
  labels:
    app.kubernetes.io/name: ai-django-backend-test-dev
    app.kubernetes.io/part-of: ai-django-backend-test
    app.kubernetes.io/component: django-backend
    app.kubernetes.io/version: "91adc17a"
    app.kubernetes.io/managed-by: argocd
    environment: dev
    app-type: django-backend
    source.repo: ChidhagniConsulting-ai-django-backend
    source.branch: 15-merge
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: ai-django-backend-test-project
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps
    targetRevision: main
    path: deployments/ai-django-backend-test/overlays/dev
  destination:
    server: https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com
    namespace: ai-django-backend-test-dev
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 2
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
  info:
  - name: Description
    value: "Development environment for ai-django-backend-test"
  - name: Repository
    value: "https://github.com/ChidhagniConsulting/ai-django-backend"
  - name: Environment
    value: "dev"
  - name: Application Type
    value: "django-backend"
  - name: Source Branch
    value: "15/merge"
  - name: Commit SHA
    value: "91adc17a"
  - name: Configuration
    value: "Development configuration with debug enabled"
