apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: ai-django-backend-test-project
  namespace: argocd
  labels:
    app.kubernetes.io/name: ai-django-backend-test-project
    app.kubernetes.io/part-of: ai-django-backend-test
    app.kubernetes.io/component: argocd-project
    app.kubernetes.io/version: "91adc17a"
    app.kubernetes.io/managed-by: argocd
    source.repo: ChidhagniConsulting-ai-django-backend
    source.branch: 15-merge
spec:
  description: "ArgoCD project for ai-django-backend-test (django-backend)"

  # Source repositories
  sourceRepos:
  - 'https://github.com/ChidhagniConsulting/ai-django-backend'
  - 'https://github.com/ChidhagniConsulting/gitops-argocd-apps'
  - 'https://github.com/ChidhagniConsulting/gitops-argocd-apps.git'
  
  # Destination clusters and namespaces
  destinations:
  # Local cluster
  - namespace: '*'
    server: https://kubernetes.default.svc
  # DigitalOcean cluster
  - namespace: '*'
    server: https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com
  # Allow any cluster
  - namespace: '*'
    server: '*'
  
  # Cluster resource whitelist
  clusterResourceWhitelist:
  - group: ''
    kind: Namespace
  - group: ''
    kind: PersistentVolume
  - group: 'rbac.authorization.k8s.io'
    kind: ClusterRole
  - group: 'rbac.authorization.k8s.io'
    kind: ClusterRoleBinding
  - group: 'apiextensions.k8s.io'
    kind: CustomResourceDefinition
  - group: 'admissionregistration.k8s.io'
    kind: MutatingWebhookConfiguration
  - group: 'admissionregistration.k8s.io'
    kind: ValidatingWebhookConfiguration
  
  # Namespace resource whitelist
  namespaceResourceWhitelist:
  - group: ''
    kind: '*'
  - group: 'apps'
    kind: '*'
  - group: 'extensions'
    kind: '*'
  - group: 'networking.k8s.io'
    kind: '*'
  - group: 'policy'
    kind: '*'
  - group: 'rbac.authorization.k8s.io'
    kind: '*'
  - group: 'autoscaling'
    kind: '*'
  - group: 'batch'
    kind: '*'
  - group: 'external-secrets.io'
    kind: '*'
  - group: 'monitoring.coreos.com'
    kind: '*'
  
  # RBAC configuration
  roles:
  - name: admin
    description: "Admin access to the project"
    policies:
    - p, proj:ai-django-backend-test-project:admin, applications, *, ai-django-backend-test-project/*, allow
    - p, proj:ai-django-backend-test-project:admin, repositories, *, *, allow
    - p, proj:ai-django-backend-test-project:admin, clusters, *, *, allow
    groups:
    - argocd-admins

  - name: developer
    description: "Developer access to the project"
    policies:
    - p, proj:ai-django-backend-test-project:developer, applications, get, ai-django-backend-test-project/*, allow
    - p, proj:ai-django-backend-test-project:developer, applications, sync, ai-django-backend-test-project/*, allow
    - p, proj:ai-django-backend-test-project:developer, applications, action/*, ai-django-backend-test-project/*, allow
    - p, proj:ai-django-backend-test-project:developer, repositories, get, *, allow
    groups:
    - argocd-developers
  
  # Sync windows (optional)
  syncWindows:
  - kind: allow
    schedule: '* * * * *'
    duration: 24h
    applications:
    - '*'
    manualSync: true
  
  # Signature keys (optional)
  signatureKeys: []
  
  # Orphaned resources (optional)
  orphanedResources:
    warn: true
