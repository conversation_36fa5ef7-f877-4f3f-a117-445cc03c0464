apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: donation-receipt-frontend-dev

resources:
- deployment.yaml
- service.yaml
- configmap.yaml


components:
- ../../components/common-labels

- ../../components/react-frontend


labels:
- pairs:
    app: donation-receipt-frontend
    app.kubernetes.io/name: donation-receipt-frontend
    app.kubernetes.io/part-of: donation-receipt-frontend
    app.kubernetes.io/component: react-frontend
    app.kubernetes.io/version: "051811ef"
    app.kubernetes.io/managed-by: argocd
    environment: dev
    source.repo: ChidhagniConsulting-Donation-Receipt-Frontend-Application
    source.branch: 48-merge

# Environment-specific configurations are now directly in the manifest files
# Database init container is now managed at the environment level via init-container-patch.yaml

patches:
- path: patch-image.yaml
  target:
    kind: Deployment
    name: donation-receipt-frontend





namePrefix: ""
nameSuffix: "-dev"
