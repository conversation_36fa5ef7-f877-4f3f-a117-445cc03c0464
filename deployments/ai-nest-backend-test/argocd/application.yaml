apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ai-nest-backend-test-staging
  namespace: argocd
  labels:
    app.kubernetes.io/name: ai-nest-backend-test-staging
    app.kubernetes.io/part-of: ai-nest-backend-test
    app.kubernetes.io/component: nest-backend
    app.kubernetes.io/version: "8d9b0f9d"
    app.kubernetes.io/managed-by: argocd
    environment: staging
    app-type: nest-backend
    source.repo: ChidhagniConsulting-gitops-argocd-apps
    source.branch: main
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: ai-nest-backend-test-project
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps
    targetRevision: main
    path: deployments/ai-nest-backend-test/overlays/staging
  destination:
    server: https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com
    namespace: ai-nest-backend-test-staging
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 2
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
  info:
  - name: Description
    value: "Staging environment for ai-nest-backend-test"
  - name: Repository
    value: "https://github.com/ChidhagniConsulting/gitops-argocd-apps"
  - name: Environment
    value: "staging"
  - name: Application Type
    value: "nest-backend"
  - name: Source Branch
    value: "main"
  - name: Commit SHA
    value: "8d9b0f9d"
  - name: Configuration
    value: "Staging configuration with production-like settings"
