apiVersion: v1
kind: Secret
metadata:
  name: ai-nest-backend-test-secrets
  labels:
    app: ai-nest-backend-test
    app.kubernetes.io/name: ai-nest-backend-test
    app.kubernetes.io/component: secrets
    app.kubernetes.io/part-of: ai-nest-backend-test
    app.kubernetes.io/version: "8d9b0f9d"
    app.kubernetes.io/managed-by: argocd
    environment: staging
type: Opaque
data:
  # Staging Environment Secrets
  # These placeholders will be replaced with base64-encoded values from secrets_encoded

  
  
  

  
  DATABASE_URL: ************************************************************************************************************************************************************************************************************************
  

  # Essential Authentication Secrets
  JWT_SECRET: c3VwZXJzZWNyZXRrZXk=
  

  # Database Credentials (Staging)
  DB_USER: bmVzdF9zdGFnaW5nX3VzZXI=
  DB_PASSWORD: QVZOU19NQlNWVmR2NGZySHY5OURIdHhT
  DB_HOST: cHJpdmF0ZS1kYmFhcy1kYi0xMDMyOTMyOC1kby11c2VyLTIzODE1NzQyLTAubS5kYi5vbmRpZ2l0YWxvY2Vhbi5jb20=
  DB_PORT: MjUwNjA=
  DB_NAME: bmVzdF9zdGFnaW5nX2Ri
  DB_SSL_MODE: cmVxdWlyZQ==

  # SMTP Configuration (Staging)
  SMTP_USER: ****************************************
  SMTP_PASS: ZnFhY3RlaGFmbXpsbHR6eg==

  # OAuth2 Configuration (Staging)
  GOOGLE_CLIENT_ID: MTA3Mzk4MTg2NDUzOC0zdWlpazcyb2hzZnIyb3Vpb3JvcjNmbTFqcWM0OTNvcy5hcHBzLmdvb2dsZXVzZXJjb250ZW50LmNvbQ==
  GOOGLE_CLIENT_SECRET: R09DU1BYLTcyRjBONEg5aGlMSVk1U3o1Z3pCczI5OEFBYlQ=

  

  
  # NestJS-specific secrets
  SESSION_SECRET: eW91ci1zdHJvbmctc2Vzc2lvbi1zZWNyZXQtaGVyZQ==
  

  # Staging-specific secrets
  # MONITORING_API_KEY: DYNAMIC_MONITORING_API_KEY_B64
  # EXTERNAL_API_KEY: DYNAMIC_EXTERNAL_API_KEY_B64
