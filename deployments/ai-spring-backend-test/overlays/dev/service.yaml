apiVersion: v1
kind: Service
metadata:
  name: ai-spring-backend-test-service
  labels:
    app: ai-spring-backend-test
    app.kubernetes.io/name: ai-spring-backend-test
    app.kubernetes.io/component: springboot-backend
    app.kubernetes.io/part-of: ai-spring-backend-test
    app.kubernetes.io/version: "003f532c"
    app.kubernetes.io/managed-by: argocd
spec:
  type: LoadBalancer
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: ai-spring-backend-test
