apiVersion: v1
kind: Service
metadata:
  name: ai-spring-backend-test-service
  labels:
    app: ai-spring-backend-test
    app.kubernetes.io/name: ai-spring-backend-test
    app.kubernetes.io/component: springboot-backend
    app.kubernetes.io/part-of: ai-spring-backend-test
    app.kubernetes.io/version: "d60cac31"
    app.kubernetes.io/managed-by: argocd
spec:
  type: LoadBalancer
  ports:
  - port: 8092
    targetPort: 8092
    protocol: TCP
    name: http
  selector:
    app: ai-spring-backend-test
