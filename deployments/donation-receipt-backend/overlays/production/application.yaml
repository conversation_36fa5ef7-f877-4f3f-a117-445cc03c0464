apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: PLACEHOLDER_PROJECT_ID-production
  namespace: argocd
  labels:
    app.kubernetes.io/name: PLACEHOLDER_PROJECT_ID-production
    app.kubernetes.io/part-of: PLACEHOLDER_APP_NAME
    app.kubernetes.io/component: PLACEHOLDER_APPLICATION_TYPE
    app.kubernetes.io/version: PLACEHOLDER_COMMIT_SHA
    app.kubernetes.io/managed-by: argocd
    environment: production
    app-type: PLACEHOLDER_APPLICATION_TYPE
    source.repo: PLACEHOLDER_SOURCE_REPO_LABEL
    source.branch: PLACEHOLDER_SOURCE_BRANCH_LABEL
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: PLACEHOLDER_PROJECT_ID-project
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps
    targetRevision: main
    path: deployments/PLACEHOLDER_PROJECT_ID/overlays/production
  destination:
    server: https://0eae25c8-1244-4c33-89fb-5e03974780a6.k8s.ondigitalocean.com
    namespace: PLACEHOLDER_PROJECT_ID-production
  syncPolicy:
    automated:
      prune: false
      selfHeal: false
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 3
      backoff:
        duration: 10s
        factor: 2
        maxDuration: 5m
  revisionHistoryLimit: 2
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
  info:
  - name: Description
    value: "Production environment for PLACEHOLDER_APP_NAME"
  - name: Repository
    value: "https://github.com/PLACEHOLDER_SOURCE_REPO"
  - name: Environment
    value: "production"
  - name: Application Type
    value: "PLACEHOLDER_APPLICATION_TYPE"
  - name: Source Branch
    value: "PLACEHOLDER_SOURCE_BRANCH"
  - name: Commit SHA
    value: PLACEHOLDER_COMMIT_SHA
  - name: Configuration
    value: "Production configuration with high availability and security"
