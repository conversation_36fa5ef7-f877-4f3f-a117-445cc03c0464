apiVersion: v1
kind: Service
metadata:
  name: donation-receipt-backend-service
  labels:
    app: donation-receipt-backend
    app.kubernetes.io/name: donation-receipt-backend
    app.kubernetes.io/component: springboot-backend
    app.kubernetes.io/part-of: donation-receipt-backend
    app.kubernetes.io/version: "357f769f"
    app.kubernetes.io/managed-by: argocd
spec:
  type: LoadBalancer
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: donation-receipt-backend
