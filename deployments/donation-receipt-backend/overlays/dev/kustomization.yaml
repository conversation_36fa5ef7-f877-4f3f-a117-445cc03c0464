apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: donation-receipt-backend-dev

resources:
- deployment.yaml
- service.yaml
- configmap.yaml
- secret.yaml

components:
- ../../components/common-labels


labels:
- pairs:
    app: donation-receipt-backend
    app.kubernetes.io/name: donation-receipt-backend
    app.kubernetes.io/part-of: donation-receipt-backend
    app.kubernetes.io/component: springboot-backend
    app.kubernetes.io/version: "357f769f"
    app.kubernetes.io/managed-by: argocd
    environment: dev
    source.repo: ChidhagniConsulting-Donation-Receipt-Backend
    source.branch: test-cases

# Environment-specific configurations are now directly in the manifest files
# Database init container is now managed at the environment level via init-container-patch.yaml

patches:
- path: patch-image.yaml
  target:
    kind: Deployment
    name: donation-receipt-backend

- path: init-container-patch.yaml
  target:
    kind: Deployment
    name: donation-receipt-backend




namePrefix: ""
nameSuffix: "-dev"
