apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: donation-receipt-backend-dev
  namespace: argocd
  labels:
    app.kubernetes.io/name: donation-receipt-backend-dev
    app.kubernetes.io/part-of: donation-receipt-backend
    app.kubernetes.io/component: springboot-backend
    app.kubernetes.io/version: "357f769f"
    app.kubernetes.io/managed-by: argocd
    environment: dev
    app-type: springboot-backend
    source.repo: ChidhagniConsulting-Donation-Receipt-Backend
    source.branch: test-cases
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: donation-receipt-backend-project
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps
    targetRevision: main
    path: deployments/donation-receipt-backend/overlays/dev
  destination:
    server: https://0eae25c8-1244-4c33-89fb-5e03974780a6.k8s.ondigitalocean.com
    namespace: donation-receipt-backend-dev
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 2
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
  info:
  - name: Description
    value: "Development environment for donation-receipt-backend"
  - name: Repository
    value: "https://github.com/ChidhagniConsulting/Donation-Receipt-Backend"
  - name: Environment
    value: "dev"
  - name: Application Type
    value: "springboot-backend"
  - name: Source Branch
    value: "test-cases"
  - name: Commit SHA
    value: "357f769f"
  - name: Configuration
    value: "Development configuration with debug enabled"
