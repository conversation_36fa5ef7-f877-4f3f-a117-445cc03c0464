apiVersion: apps/v1
kind: Deployment
metadata:
  name: donation-receipt-backend
  labels:
    app: donation-receipt-backend
    app.kubernetes.io/name: donation-receipt-backend
    app.kubernetes.io/component: springboot-backend
    app.kubernetes.io/part-of: donation-receipt-backend
    app.kubernetes.io/version: "357f769f"
    app.kubernetes.io/managed-by: argocd
spec:
  replicas: 1
  selector:
    matchLabels:
      app: donation-receipt-backend
  template:
    metadata:
      labels:
        app: donation-receipt-backend
        app.kubernetes.io/name: donation-receipt-backend
        app.kubernetes.io/component: springboot-backend
        app.kubernetes.io/part-of: donation-receipt-backend
        app.kubernetes.io/version: "357f769f"
    spec:
      containers:
      - name: donation-receipt-backend
        image: registry.digitalocean.com/chidhagni-doks-registry/donation-receipt-backend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        envFrom:
        - configMapRef:
            name: donation-receipt-backend-config
       
        env:
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: donation-receipt-backend-secrets
              key: JWT_SECRET
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: donation-receipt-backend-secrets
              key: DB_USER
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: donation-receipt-backend-secrets
              key: DB_PASSWORD
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: donation-receipt-backend-secrets
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            secretKeyRef:
              name: donation-receipt-backend-secrets
              key: DB_PORT
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: donation-receipt-backend-secrets
              key: DB_NAME
        - name: DB_SSL_MODE
          valueFrom:
            secretKeyRef:
              name: donation-receipt-backend-secrets
              key: DB_SSL_MODE
        
        # Spring Boot specific database configuration
        - name: SPRING_DATASOURCE_URL
          valueFrom:
            secretKeyRef:
              name: donation-receipt-backend-secrets
              key: SPRING_DATASOURCE_URL

        - name: SPRING_DATASOURCE_USERNAME
          valueFrom:
            secretKeyRef:
              name: donation-receipt-backend-secrets
              key: DB_USER
        - name: SPRING_DATASOURCE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: donation-receipt-backend-secrets
              key: DB_PASSWORD
        - name: SPRING_DATASOURCE_DRIVER_CLASS_NAME
          value: "org.postgresql.Driver"
        
        - name: SMTP_USER
          valueFrom:
            secretKeyRef:
              name: donation-receipt-backend-secrets
              key: SMTP_USER
        - name: SMTP_PASS
          valueFrom:
            secretKeyRef:
              name: donation-receipt-backend-secrets
              key: SMTP_PASS
        - name: GOOGLE_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: donation-receipt-backend-secrets
              key: GOOGLE_CLIENT_ID
        - name: GOOGLE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: donation-receipt-backend-secrets
              key: GOOGLE_CLIENT_SECRET
        
        # Spring Boot Health Checks (Development - faster startup)
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 90
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 90
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
