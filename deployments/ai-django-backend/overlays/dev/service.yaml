apiVersion: v1
kind: Service
metadata:
  name: ai-django-backend-service
  labels:
    app: ai-django-backend
    app.kubernetes.io/name: ai-django-backend
    app.kubernetes.io/component: django-backend
    app.kubernetes.io/part-of: ai-django-backend
    app.kubernetes.io/version: "15341274"
    app.kubernetes.io/managed-by: argocd
spec:
  type: LoadBalancer
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
    name: http
  selector:
    app: ai-django-backend
