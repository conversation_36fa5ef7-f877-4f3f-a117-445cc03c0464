apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-django-backend-config
  labels:
    app: ai-django-backend
    app.kubernetes.io/name: ai-django-backend
    app.kubernetes.io/component: config
    app.kubernetes.io/part-of: ai-django-backend
    app.kubernetes.io/version: "f4509a52"
    app.kubernetes.io/managed-by: argocd
data:
  # Application Configuration
  APP_NAME: "ai-django-backend"
  PROJECT_ID: "ai-django-backend"
  APPLICATION_TYPE: "django-backend"
  SOURCE_REPO: "ChidhagniConsulting/gitops-argocd-apps"
  SOURCE_BRANCH: "main"
  COMMIT_SHA: "f4509a52"
 
  NODE_ENV: "staging"
  PORT: "8001"
 
  
  # Database Configuration (DigitalOcean PostgreSQL)
  # Database connection details are configured via environment variables in deployment from secrets
  
  # Application URLs
  APP_URL: "http://localhost:8001"
  API_URL: "http://localhost:8001/api"
  
  # Common Backend Configuration
  SERVER_PORT: "8001"

  

  
  # Django Configuration
  DJANGO_SETTINGS_MODULE: "app.settings"
  DEBUG: "False"
  ALLOWED_HOSTS: "*"
  DJANGO_ENV: "staging"

  # Django Static Files
  STATIC_URL: "/static/"
  STATIC_ROOT: "/app/staticfiles/"
  MEDIA_URL: "/media/"
  MEDIA_ROOT: "/app/media/"

  # Django CORS Configuration
  CORS_ALLOWED_ORIGINS: "http://localhost:3000,http://127.0.0.1:3000"
  CORS_ALLOW_CREDENTIALS: "true"

  # Django Database Configuration
  DATABASE_ENGINE: "django.db.backends.postgresql"
  DATABASE_CONN_MAX_AGE: "600"
  DATABASE_CONN_HEALTH_CHECKS: "True"
  DATABASE_OPTIONS_CONNECT_TIMEOUT: "30"
  DATABASE_OPTIONS_COMMAND_TIMEOUT: "60"
  DATABASE_OPTIONS_SERVER_SIDE_BINDING: "True"
  

  

  
