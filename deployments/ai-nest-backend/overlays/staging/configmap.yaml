apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-nest-backend-config
  labels:
    app: ai-nest-backend
    app.kubernetes.io/name: ai-nest-backend
    app.kubernetes.io/component: config
    app.kubernetes.io/part-of: ai-nest-backend
    app.kubernetes.io/version: "fb4ad9ad"
    app.kubernetes.io/managed-by: argocd
data:
  # Application Configuration
  APP_NAME: "ai-nest-backend"
  PROJECT_ID: "ai-nest-backend"
  APPLICATION_TYPE: "nest-backend"
  SOURCE_REPO: "ChidhagniConsulting/gitops-argocd-apps"
  SOURCE_BRANCH: "main"
  COMMIT_SHA: "fb4ad9ad"
 
  NODE_ENV: "staging"
  PORT: "3002"
 
  
  # Database Configuration (DigitalOcean PostgreSQL)
  # Database connection details are configured via environment variables in deployment from secrets
  
  # Application URLs
  APP_URL: "http://localhost:3002"
  API_URL: "http://localhost:3002/api"
  
  # Common Backend Configuration
  SERVER_PORT: "3002"

  

  

  

  # NestJS CORS Configuration
  CORS_ORIGIN: "http://localhost:3000,http://127.0.0.1:3000"
  CORS_CREDENTIALS: "true"

  # TypeORM Configuration
  TYPEORM_SYNCHRONIZE: "false"
  TYPEORM_LOGGING: "false"
  TYPEORM_ENTITIES: "dist/**/*.entity.js"
  TYPEORM_MIGRATIONS: "dist/migrations/*.js"
  TYPEORM_MIGRATIONS_DIR: "src/migrations"
  TYPEORM_MAX_QUERY_EXECUTION_TIME: "30000"
  TYPEORM_POOL_SIZE: "10"
  TYPEORM_CONNECTION_TIMEOUT: "30000"
  TYPEORM_ACQUIRE_TIMEOUT: "60000"
  TYPEORM_TIMEOUT: "60000"
  

  
