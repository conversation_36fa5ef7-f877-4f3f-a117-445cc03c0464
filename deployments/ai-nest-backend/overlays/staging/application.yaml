apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ai-nest-backend-staging
  namespace: argocd
  labels:
    app.kubernetes.io/name: ai-nest-backend-staging
    app.kubernetes.io/part-of: ai-nest-backend
    app.kubernetes.io/component: nest-backend
    app.kubernetes.io/version: "fb4ad9ad"
    app.kubernetes.io/managed-by: argocd
    environment: staging
    app-type: nest-backend
    source.repo: ChidhagniConsulting-gitops-argocd-apps
    source.branch: main
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: ai-nest-backend-project
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps
    targetRevision: main
    path: deployments/ai-nest-backend/overlays/staging
  destination:
    server: https://0eae25c8-1244-4c33-89fb-5e03974780a6.k8s.ondigitalocean.com
    namespace: ai-nest-backend-staging
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 2
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
  info:
  - name: Description
    value: "Staging environment for ai-nest-backend"
  - name: Repository
    value: "https://github.com/ChidhagniConsulting/gitops-argocd-apps"
  - name: Environment
    value: "staging"
  - name: Application Type
    value: "nest-backend"
  - name: Source Branch
    value: "main"
  - name: Commit SHA
    value: "fb4ad9ad"
  - name: Configuration
    value: "Staging configuration with production-like settings"
