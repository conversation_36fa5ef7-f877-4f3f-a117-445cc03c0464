name: 🎯 Deploy to STAGING Environment

on:
  workflow_dispatch:
    inputs:
      project_id:
        description: 'Project ID to deploy'
        required: true
        type: string
      docker_image:
        description: 'Docker image (without tag)'
        required: true
        type: string
      docker_tag:
        description: 'Docker tag to deploy'
        required: true
        type: string
      container_port:
        description: 'container port'
        required: true
        type: string
      application_type:
        description: 'Application type'
        required: true
        type: choice
        options:
          - react-frontend
          - springboot-backend
          - django-backend
          - nest-backend
          - web-app
          - api
          - microservice
        default: web-app
      skip_approval:
        description: 'Skip approval process (for testing)'
        required: false
        type: boolean
        default: false

env:
  ENVIRONMENT: staging
  GITOPS_TOKEN: ${{ secrets.GITOPS_TOKEN }}
  DIGITALOCEAN_ACCESS_TOKEN: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

jobs:
  validate-staging-deployment:
    name: 🔍 Validate Staging Deployment
    runs-on: [self-hosted, Linux]
    outputs:
      should-deploy: ${{ steps.validate.outputs.should-deploy }}
      project-id: ${{ steps.validate.outputs.project-id }}
      docker-image: ${{ steps.validate.outputs.docker-image }}
      docker-tag: ${{ steps.validate.outputs.docker-tag }}
      container-port: ${{ steps.validate.outputs.container-port }}
      application-type: ${{ steps.validate.outputs.application-type }}
      skip-approval: ${{ steps.validate.outputs.skip-approval }}
    steps:
      - name: 🔍 Validate Deployment Request
        id: validate
        run: |
          echo "=== STAGING DEPLOYMENT VALIDATION ==="
          echo "Trigger: Manual workflow dispatch"
          echo "Repository: ${{ github.repository }}"
          echo "Triggered by: ${{ github.actor }}"
          echo "Workflow run ID: ${{ github.run_id }}"
          echo "=================================="

          PROJECT_ID="${{ github.event.inputs.project_id }}"
          DOCKER_IMAGE="${{ github.event.inputs.docker_image }}"
          DOCKER_TAG="${{ github.event.inputs.docker_tag }}"
          CONTAINER_PORT="${{ github.event.inputs.container_port }}"
          APPLICATION_TYPE="${{ github.event.inputs.application_type }}"
          SKIP_APPROVAL="${{ github.event.inputs.skip_approval }}"

          # Validate required fields
          if [ -z "$PROJECT_ID" ] || [ -z "$DOCKER_IMAGE" ] || [ -z "$DOCKER_TAG" ]; then
            echo "❌ Missing required fields: project_id, docker_image, docker_tag"
            echo "should-deploy=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Validate project ID format
          if ! echo "$PROJECT_ID" | grep -qE '^[a-z0-9-]+$'; then
            echo "❌ Invalid project ID format: $PROJECT_ID"
            echo "Project ID must be lowercase alphanumeric with hyphens only"
            echo "should-deploy=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Set default application type if not provided
          if [ -z "$APPLICATION_TYPE" ]; then
            APPLICATION_TYPE="web-app"
            echo "⚠️ No application_type provided, defaulting to: $APPLICATION_TYPE"
          fi

          # Validate application type and check required secrets availability
          case "$APPLICATION_TYPE" in
            "springboot-backend")
              echo "🔍 Validating Spring Boot application secrets..."
              REQUIRED_SECRETS=("JWT_SECRET_SPRING" "DB_HOST_SPRING_STAGING" "DB_USER_SPRING_STAGING" "DB_PASSWORD_SPRING_STAGING")
              ;;
            "nest-backend")
              echo "🔍 Validating NestJS application secrets..."
              REQUIRED_SECRETS=("JWT_SECRET_NEST" "SESSION_SECRET_NEST" "DB_HOST_NEST_STAGING" "DB_USER_NEST_STAGING" "DB_PASSWORD_NEST_STAGING")
              ;;
            "django-backend")
              echo "🔍 Validating Django application secrets..."
              REQUIRED_SECRETS=("JWT_SECRET_DJANGO" "DJANGO_SECRET_KEY" "DB_HOST_DJANGO_STAGING" "DB_USER_DJANGO_STAGING" "DB_PASSWORD_DJANGO_STAGING")
              ;;
            "react-frontend"|"web-app"|"api"|"microservice")
              echo "🔍 Frontend/Generic application - using default Spring Boot secrets..."
              REQUIRED_SECRETS=("JWT_SECRET_SPRING" "DB_HOST_SPRING_STAGING" "DB_USER_SPRING_STAGING" "DB_PASSWORD_SPRING_STAGING")
              ;;
            *)
              echo "⚠️ Unknown application type: $APPLICATION_TYPE, using default validation"
              REQUIRED_SECRETS=("JWT_SECRET_SPRING" "DB_HOST_SPRING_STAGING" "DB_USER_SPRING_STAGING" "DB_PASSWORD_SPRING_STAGING")
              ;;
          esac

          echo "📋 Required secrets for $APPLICATION_TYPE: ${REQUIRED_SECRETS[*]}"
          echo "✅ Application type validation completed"

          # Set default for skip_approval if not provided
          if [ -z "$SKIP_APPROVAL" ]; then
            SKIP_APPROVAL="false"
          fi

          echo "✅ Staging deployment validation passed"
          echo "should-deploy=true" >> $GITHUB_OUTPUT
          echo "project-id=$PROJECT_ID" >> $GITHUB_OUTPUT
          echo "docker-image=$DOCKER_IMAGE" >> $GITHUB_OUTPUT
          echo "docker-tag=$DOCKER_TAG" >> $GITHUB_OUTPUT
          echo "container-port=$CONTAINER_PORT" >> $GITHUB_OUTPUT
          echo "application-type=$APPLICATION_TYPE" >> $GITHUB_OUTPUT
          echo "skip-approval=$SKIP_APPROVAL" >> $GITHUB_OUTPUT

          echo "📋 Staging Deployment Details:"
          echo "  Project: $PROJECT_ID"
          echo "  Docker Image: $DOCKER_IMAGE:$DOCKER_TAG"
          echo "  Application Type: $APPLICATION_TYPE"
          echo "  Container Port: $CONTAINER_PORT"
          echo "  Skip Approval: $SKIP_APPROVAL"

  approval-gate:
    name: 🛡️ Staging Approval Gate
    needs: validate-staging-deployment
    if: needs.validate-staging-deployment.outputs.should-deploy == 'true' && needs.validate-staging-deployment.outputs.skip-approval == 'false'
    runs-on: [self-hosted, Linux]
    environment: staging
    outputs:
      approved: ${{ steps.approval.outputs.approved }}
    steps:
      - name: 🛡️ Staging Deployment Approval
        id: approval
        run: |
          echo "🛡️ Staging deployment approval process..."
          echo ""
          echo "📋 Deployment Request:"
          echo "  • Project: ${{ needs.validate-staging-deployment.outputs.project-id }}"
          echo "  • Docker Image: ${{ needs.validate-staging-deployment.outputs.docker-image }}:${{ needs.validate-staging-deployment.outputs.docker-tag }}"
          echo "  • Application Type: ${{ needs.validate-staging-deployment.outputs.application-type }}"
          echo "  • Container Port: ${{ needs.validate-staging-deployment.outputs.container-port }}"
          echo "  • Environment: STAGING"
          echo "  • Requested by: ${{ github.actor }}"
          echo ""
          echo "✅ Approval granted for staging deployment"
          echo "approved=true" >> $GITHUB_OUTPUT

  deploy-staging:
    name: 🚀 Deploy to STAGING
    needs: [validate-staging-deployment, approval-gate]
    if: |
      needs.validate-staging-deployment.outputs.should-deploy == 'true' && 
      (needs.validate-staging-deployment.outputs.skip-approval == 'true' || 
       needs.approval-gate.outputs.approved == 'true')
    runs-on: [self-hosted, Linux]
    environment: staging
    outputs:
      deployment-success: ${{ steps.deploy.outputs.success }}
      deployment-url: ${{ steps.deploy.outputs.url }}
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITOPS_TOKEN }}
          fetch-depth: 0

      - name: 🔧 Setup kubectl
        id: setup-kubectl
        run: |
          echo "🔧 Setting up kubectl for STAGING environment..."
          
          # Install kubectl if not available
          if ! command -v kubectl >/dev/null 2>&1; then
            echo "📦 Installing kubectl..."
            curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
            chmod +x kubectl
            sudo mv kubectl /usr/local/bin/
            echo "✅ kubectl installed successfully"
          else
            echo "✅ kubectl is already available"
          fi

          # Install doctl if not available
          if ! command -v doctl >/dev/null 2>&1; then
            echo "📦 Installing doctl..."
            cd /tmp
            wget -q "https://github.com/digitalocean/doctl/releases/download/v1.104.0/doctl-1.104.0-linux-amd64.tar.gz"
            tar xf "doctl-1.104.0-linux-amd64.tar.gz"
            sudo mv doctl /usr/local/bin/
            sudo chmod +x /usr/local/bin/doctl
            rm -f "doctl-1.104.0-linux-amd64.tar.gz"
            echo "✅ doctl installed successfully"
          else
            echo "✅ doctl is already available"
          fi

          # Authenticate with DigitalOcean
          echo "🔐 Authenticating with DigitalOcean..."
          if ! doctl auth init --access-token "$DIGITALOCEAN_ACCESS_TOKEN"; then
            echo "❌ Failed to authenticate with DigitalOcean"
            echo "cluster-accessible=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Configure kubectl for ArgoCD Management Cluster
          CLUSTER_ID="ca0e9f31-fd81-43a8-bace-ef88bb156117"
          echo "⚙️ Configuring kubectl for ArgoCD Management cluster: $CLUSTER_ID"
          
          if ! doctl kubernetes cluster kubeconfig save "$CLUSTER_ID"; then
            echo "❌ Failed to configure kubectl for ArgoCD Management cluster"
            echo "cluster-accessible=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Test cluster connectivity
          if kubectl cluster-info >/dev/null 2>&1; then
            echo "cluster-accessible=true" >> $GITHUB_OUTPUT
            echo "✅ ArgoCD Management cluster is accessible"
          else
            echo "cluster-accessible=false" >> $GITHUB_OUTPUT
            echo "❌ ArgoCD Management cluster is not accessible"
            exit 0
          fi

          # Check ArgoCD availability
          if kubectl get namespace argocd >/dev/null 2>&1; then
            echo "argocd-available=true" >> $GITHUB_OUTPUT
            echo "✅ ArgoCD namespace exists"
          else
            echo "argocd-available=false" >> $GITHUB_OUTPUT
            echo "❌ ArgoCD namespace not found"
            exit 0
          fi

      - name: 🎯 Prepare Staging Promotion Payload & Secrets
        id: prepare
        run: |
          echo "🚀 Preparing staging deployment..."
          echo "📋 Deployment Details:"
          echo "  • Project: ${{ needs.validate-staging-deployment.outputs.project-id }}"
          echo "  • Docker Image: ${{ needs.validate-staging-deployment.outputs.docker-image }}:${{ needs.validate-staging-deployment.outputs.docker-tag }}"
          echo "  • Container Port: ${{ needs.validate-staging-deployment.outputs.container-port }}"
          echo "  • Application Type: ${{ needs.validate-staging-deployment.outputs.application-type }}"

          PROJECT_ID="${{ needs.validate-staging-deployment.outputs.project-id }}"
          DOCKER_IMAGE="${{ needs.validate-staging-deployment.outputs.docker-image }}"
          DOCKER_TAG="${{ needs.validate-staging-deployment.outputs.docker-tag }}"
          CONTAINER_PORT="${{ needs.validate-staging-deployment.outputs.container-port }}"
          APPLICATION_TYPE="${{ needs.validate-staging-deployment.outputs.application-type }}"

          # Generate staging docker tag (ensure it has staging identifier)
          if [[ "$DOCKER_TAG" == *"staging"* ]]; then
            STAGING_TAG="$DOCKER_TAG"
          else
            STAGING_TAG=$(echo "$DOCKER_TAG" | sed 's/dev/staging/g')
            # If no 'dev' to replace, append staging
            if [ "$STAGING_TAG" = "$DOCKER_TAG" ]; then
              STAGING_TAG="${DOCKER_TAG}-staging"
            fi
          fi

          echo "  • Original Tag: $DOCKER_TAG"
          echo "  • Staging Tag: $STAGING_TAG"

          echo "🔐 Preparing staging secrets for application type: $APPLICATION_TYPE"

          # Prepare secrets based on application type
          case "$APPLICATION_TYPE" in
            "springboot-backend")
              echo "📦 Configuring Spring Boot secrets..."
              STAGING_DB_SECRETS=$(cat << EOF | jq -c .
              {
                "JWT_SECRET": "${{ secrets.JWT_SECRET_SPRING }}",
                "ENABLE_DATABASE": "${{ secrets.ENABLE_DATABASE_SPRING }}",
                "DB_HOST": "${{ secrets.DB_HOST_SPRING_STAGING }}",
                "DB_USER": "${{ secrets.DB_USER_SPRING_STAGING }}",
                "DB_PASSWORD": "${{ secrets.DB_PASSWORD_SPRING_STAGING }}",
                "DB_NAME": "${{ secrets.DB_NAME_SPRING_STAGING }}",
                "DB_PORT": "${{ secrets.DB_PORT_SPRING }}",
                "DB_SSL_MODE": "${{ secrets.DB_SSL_MODE_SPRING }}",
                "SMTP_USER": "${{ secrets.SMTP_USER_SPRING }}",
                "SMTP_PASS": "${{ secrets.SMTP_PASS_SPRING }}",
                "GOOGLE_CLIENT_ID": "${{ secrets.GOOGLE_CLIENT_ID_SPRING }}",
                "GOOGLE_CLIENT_SECRET": "${{ secrets.GOOGLE_CLIENT_SECRET_SPRING }}"
              }
          EOF
              )
              ;;
            "nest-backend")
              echo "📦 Configuring NestJS secrets..."
              STAGING_DB_SECRETS=$(cat << EOF | jq -c .
              {
                "JWT_SECRET": "${{ secrets.JWT_SECRET_NEST }}",
                "SESSION_SECRET": "${{ secrets.SESSION_SECRET_NEST }}",
                "ENABLE_DATABASE": "${{ secrets.ENABLE_DATABASE_NEST }}",
                "DB_HOST": "${{ secrets.DB_HOST_NEST_STAGING }}",
                "DB_USER": "${{ secrets.DB_USER_NEST_STAGING }}",
                "DB_PASSWORD": "${{ secrets.DB_PASSWORD_NEST_STAGING }}",
                "DB_NAME": "${{ secrets.DB_NAME_NEST_STAGING }}",
                "DB_PORT": "${{ secrets.DB_PORT_NEST }}",
                "DB_SSL_MODE": "${{ secrets.DB_SSL_MODE_NEST }}",
                "SMTP_USER": "${{ secrets.SMTP_USER_NEST }}",
                "SMTP_PASS": "${{ secrets.SMTP_PASS_NEST }}",
                "GOOGLE_CLIENT_ID": "${{ secrets.GOOGLE_CLIENT_ID_NEST }}",
                "GOOGLE_CLIENT_SECRET": "${{ secrets.GOOGLE_CLIENT_SECRET_NEST }}"
              }
          EOF
              )
              ;;
            "django-backend")
              echo "📦 Configuring Django secrets..."
              STAGING_DB_SECRETS=$(cat << EOF | jq -c .
              {
                "JWT_SECRET": "${{ secrets.JWT_SECRET_DJANGO }}",
                "DJANGO_SECRET_KEY": "${{ secrets.DJANGO_SECRET_KEY }}",
                "SESSION_SECRET": "${{ secrets.SESSION_SECRET_DJANGO }}",
                "ENABLE_DATABASE": "${{ secrets.ENABLE_DATABASE_DJANGO }}",
                "DB_HOST": "${{ secrets.DB_HOST_DJANGO_STAGING }}",
                "DB_USER": "${{ secrets.DB_USER_DJANGO_STAGING }}",
                "DB_PASSWORD": "${{ secrets.DB_PASSWORD_DJANGO_STAGING }}",
                "DB_NAME": "${{ secrets.DB_NAME_DJANGO_STAGING }}",
                "DB_PORT": "${{ secrets.DB_PORT_DJANGO }}",
                "DB_SSL_MODE": "${{ secrets.DB_SSL_MODE_DJANGO }}",
                "SMTP_USER": "${{ secrets.SMTP_USER_DJANGO }}",
                "SMTP_PASS": "${{ secrets.SMTP_PASS_DJANGO }}",
                "GOOGLE_CLIENT_ID": "${{ secrets.GOOGLE_CLIENT_ID_DJANGO }}",
                "GOOGLE_CLIENT_SECRET": "${{ secrets.GOOGLE_CLIENT_SECRET_DJANGO }}",
                "RATE_LIMIT_WINDOW_MS": "${{ secrets.RATE_LIMIT_WINDOW_MS_DJANGO }}",
                "RATE_LIMIT_MAX_REQUESTS": "${{ secrets.RATE_LIMIT_MAX_REQUESTS_DJANGO }}",
                "PASSWORD_RESET_TOKEN_EXPIRY": "${{ secrets.PASSWORD_RESET_TOKEN_EXPIRY_DJANGO }}",
                "EMAIL_VERIFICATION_TOKEN_EXPIRY": "${{ secrets.EMAIL_VERIFICATION_TOKEN_EXPIRY_DJANGO }}"
              }
          EOF
              )
              ;;
            *)
              echo "⚠️ Unknown application type: $APPLICATION_TYPE, using default Spring Boot configuration"
              STAGING_DB_SECRETS=$(cat << EOF | jq -c .
              {
                "JWT_SECRET": "${{ secrets.JWT_SECRET_SPRING }}",
                "ENABLE_DATABASE": "${{ secrets.ENABLE_DATABASE_SPRING }}",
                "DB_HOST": "${{ secrets.DB_HOST_SPRING_STAGING }}",
                "DB_USER": "${{ secrets.DB_USER_SPRING_STAGING }}",
                "DB_PASSWORD": "${{ secrets.DB_PASSWORD_SPRING_STAGING }}",
                "DB_NAME": "${{ secrets.DB_NAME_SPRING_STAGING }}",
                "DB_PORT": "${{ secrets.DB_PORT_SPRING }}",
                "DB_SSL_MODE": "${{ secrets.DB_SSL_MODE_SPRING }}",
                "SMTP_USER": "${{ secrets.SMTP_USER_SPRING }}",
                "SMTP_PASS": "${{ secrets.SMTP_PASS_SPRING }}",
                "GOOGLE_CLIENT_ID": "${{ secrets.GOOGLE_CLIENT_ID_SPRING }}",
                "GOOGLE_CLIENT_SECRET": "${{ secrets.GOOGLE_CLIENT_SECRET_SPRING }}"
              }
          EOF
              )
              ;;
          esac

          STAGING_SECRETS_ENCODED=$(echo "$STAGING_DB_SECRETS" | base64 -w 0)

          STAGING_PAYLOAD=$(cat << EOF | jq -c .
          {
            "project_id": "$PROJECT_ID",
            "application_type": "$APPLICATION_TYPE",
            "environment": "staging",
            "docker_image": "$DOCKER_IMAGE",
            "docker_tag": "$STAGING_TAG",
            "container_port": "$CONTAINER_PORT",
            "source_repo": "${{ github.repository }}",
            "source_branch": "main",
            "commit_sha": "${{ github.sha }}",
            "secrets_encoded": "$STAGING_SECRETS_ENCODED"
          }
          EOF
          )

          echo "$STAGING_PAYLOAD" > payload.json
          echo "staging-tag=$STAGING_TAG" >> $GITHUB_OUTPUT
          echo "✅ Staging promotion payload prepared for $APPLICATION_TYPE application."

      - name: 🐳 Promote Docker Image to Staging
        id: promote-image
        run: |
          echo "🐳 Promoting Docker image to staging..."

          DOCKER_IMAGE="${{ needs.validate-staging-deployment.outputs.docker-image }}"
          ORIGINAL_TAG="${{ needs.validate-staging-deployment.outputs.docker-tag }}"
          STAGING_TAG="${{ steps.prepare.outputs.staging-tag }}"

          SOURCE_IMAGE="$DOCKER_IMAGE:$ORIGINAL_TAG"
          TARGET_IMAGE="$DOCKER_IMAGE:$STAGING_TAG"

          echo "📋 Source: $SOURCE_IMAGE"
          echo "📋 Target: $TARGET_IMAGE"

          if ! command -v doctl &> /dev/null; then
            echo "📦 Installing doctl..."
            cd /tmp
            wget -q https://github.com/digitalocean/doctl/releases/download/v1.104.0/doctl-1.104.0-linux-amd64.tar.gz
            tar xf doctl-1.104.0-linux-amd64.tar.gz
            sudo mv doctl /usr/local/bin
          fi

          echo "🔐 Authenticating with DigitalOcean..."
          doctl auth init --access-token "${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}"
          doctl registry login

          echo "📥 Pulling source image..."
          if ! docker pull "$SOURCE_IMAGE"; then
            echo "❌ Failed to pull source image"
            exit 1
          fi

          echo "🏷️ Tagging as staging..."
          docker tag "$SOURCE_IMAGE" "$TARGET_IMAGE"

          echo "📤 Pushing to DOCR..."
          if ! docker push "$TARGET_IMAGE"; then
            echo "❌ Failed to push staging image"
            exit 1
          fi

          echo "🧹 Cleaning up local Docker images..."
          docker rmi "$SOURCE_IMAGE" "$TARGET_IMAGE" || true

          echo "✅ Docker image promoted to staging."

      - name: 🚀 Generate Staging Manifests
        id: generate-manifests
        run: |
          echo "🚀 Generating staging deployment manifests..."

          PROJECT_ID="${{ needs.validate-staging-deployment.outputs.project-id }}"
          DOCKER_IMAGE="${{ needs.validate-staging-deployment.outputs.docker-image }}"
          STAGING_TAG="${{ steps.prepare.outputs.staging-tag }}"
          APPLICATION_TYPE="${{ needs.validate-staging-deployment.outputs.application-type }}"
          CONTAINER_PORT="${{ needs.validate-staging-deployment.outputs.container-port }}"

          echo "📦 Manifest generation payload:"
          cat payload.json | jq .

          echo "🐍 Checking Python environment..."
          python3 --version || python --version

          echo "📦 Installing Python dependencies..."
          pip3 install PyYAML --user --quiet || echo "⚠️ PyYAML installation failed, will use basic processing"

          if [ ! -f "scripts/deploy.py" ]; then
            echo "❌ deploy.py script not found"
            exit 1
          fi

          echo "✅ deploy.py script found"

          # Use manifests directory as template source
          TEMPLATE_DIR="manifests"

          # Check if template directory exists
          if [ ! -d "$TEMPLATE_DIR" ]; then
            echo "❌ Template directory not found: $TEMPLATE_DIR"
            echo "Available directories:"
            ls -la . || echo "Current directory listing failed"
            exit 1
          fi

          echo "✅ Using template directory: $TEMPLATE_DIR"
          echo "📁 Will create project directory: deployments/$PROJECT_ID"

          # Extract payload from file
          PAYLOAD=$(cat payload.json)

          python3 scripts/deploy.py \
            --payload "$PAYLOAD" \
            --manifest-dir "$TEMPLATE_DIR" \
            --output-dir "generated-manifests" \
            --skip-validation \
            --dry-run

          status=$?
          if [ $status -eq 0 ]; then
            echo "success=true" >> $GITHUB_OUTPUT
            PROJECT_PATH="deployments/$PROJECT_ID"
            echo "project-path=$PROJECT_PATH" >> $GITHUB_OUTPUT
            echo "✅ Manifest generation completed successfully"
            if [ ! -d "$PROJECT_PATH" ]; then
              echo "⚠️ Generated directory $PROJECT_PATH not found"
              echo "Available directories in deployments:"
              ls -la deployments/ || echo "deployments/ directory not found or empty"
              exit 1
            fi
            echo "📁 Generated directory structure:"
            find "$PROJECT_PATH" -type f | sort
          else
            echo "success=false" >> $GITHUB_OUTPUT
            echo "❌ Manifest generation failed with exit code: $status"
            exit 1
          fi

      - name: 🔧 Configure Git
        if: steps.generate-manifests.outputs.success == 'true'
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"

      - name: 💾 Commit Generated Files
        id: commit
        if: steps.generate-manifests.outputs.success == 'true'
        run: |
          git add "${{ steps.generate-manifests.outputs.project-path }}/"
          if git diff --staged --quiet; then
            echo "No changes to commit"
            echo "committed=false" >> $GITHUB_OUTPUT
          else
            git commit -m "🎯 Deploy ${{ needs.validate-staging-deployment.outputs.project-id }} to STAGING environment

            Triggered by manual staging deployment workflow

            - Application Type: ${{ needs.validate-staging-deployment.outputs.application-type }}
            - Docker Image: ${{ needs.validate-staging-deployment.outputs.docker-image }}:${{ steps.prepare.outputs.staging-tag }}
            - Environment: staging
            - Container Port: ${{ needs.validate-staging-deployment.outputs.container-port }}
            - Deployed by: ${{ github.actor }}
            - ArgoCD Application and Project manifests
            - Complete Kubernetes deployment manifests

            Auto-generated by GitOps CI/CD integration"
            echo "committed=true" >> $GITHUB_OUTPUT
            echo "✅ Changes committed successfully"
          fi

      - name: 🚀 Push Changes
        if: steps.commit.outputs.committed == 'true'
        run: |
          git push origin main
          echo "✅ Changes pushed to main branch"

      - name: 🚀 Deploy to ArgoCD
        id: deploy
        if: steps.generate-manifests.outputs.success == 'true'
        run: |
          PROJECT_ID="${{ needs.validate-staging-deployment.outputs.project-id }}"
          ENVIRONMENT="staging"
          CONTAINER_PORT="${{ needs.validate-staging-deployment.outputs.container-port }}"
          DOCKER_IMAGE="${{ needs.validate-staging-deployment.outputs.docker-image }}"
          DOCKER_TAG="${{ steps.prepare.outputs.staging-tag }}"

          echo "🚀 Starting ArgoCD deployment for: $PROJECT_ID"
          echo "🌍 Environment: $ENVIRONMENT"
          echo "🐳 Docker Image: $DOCKER_IMAGE:$DOCKER_TAG"

          # Pull latest changes to ensure we have the generated manifests
          git pull origin main

          # Verify manifest files exist
          PROJECT_FILE="deployments/$PROJECT_ID/argocd/project.yaml"
          APPLICATION_FILE="deployments/$PROJECT_ID/overlays/$ENVIRONMENT/application.yaml"

          if [ ! -f "$PROJECT_FILE" ] || [ ! -f "$APPLICATION_FILE" ]; then
            echo "❌ ArgoCD manifest files not found: $PROJECT_FILE, $APPLICATION_FILE"
            echo "success=false" >> $GITHUB_OUTPUT
            exit 1
          fi

          echo "✅ Manifest files found"
          echo "📋 Project file: $PROJECT_FILE"
          echo "🎯 Application file: $APPLICATION_FILE"

          # Apply ArgoCD Project
          echo "📋 Applying ArgoCD Project..."
          if kubectl apply -f "$PROJECT_FILE"; then
            echo "✅ ArgoCD Project applied successfully"
          else
            echo "❌ Failed to apply ArgoCD Project"
            echo "success=false" >> $GITHUB_OUTPUT
            exit 1
          fi

          # Apply ArgoCD Application
          echo "🎯 Applying ArgoCD Application..."
          if kubectl apply -f "$APPLICATION_FILE"; then
            echo "✅ ArgoCD Application applied successfully"
          else
            echo "❌ Failed to apply ArgoCD Application"
            echo "success=false" >> $GITHUB_OUTPUT
            exit 1
          fi

          # Wait for application to be created
          echo "⏳ Waiting for ArgoCD application to be created..."
          for i in {1..30}; do
            if kubectl get application "$PROJECT_ID" -n argocd >/dev/null 2>&1; then
              echo "✅ ArgoCD application '$PROJECT_ID' created successfully"
              break
            fi
            echo "⏳ Waiting for application creation... ($i/30)"
            sleep 2
          done

          # Trigger sync
          if kubectl get application "$PROJECT_ID" -n argocd >/dev/null 2>&1; then
            echo "🔄 Triggering ArgoCD application sync..."
            kubectl patch application "$PROJECT_ID" -n argocd --type merge -p '{"operation":{"sync":{}}}' 2>/dev/null || true
            echo "✅ ArgoCD sync triggered"
          fi

          echo "success=true" >> $GITHUB_OUTPUT
          echo "✅ STAGING deployment completed successfully"

      - name: 🎉 Staging Deployment Success
        if: steps.deploy.outputs.success == 'true'
        run: |
          echo "🎉 STAGING Environment Deployment Successful!"
          echo ""
          echo "📊 Deployment Summary:"
          echo "  • Environment: STAGING"
          echo "  • Project: ${{ needs.validate-staging-deployment.outputs.project-id }}"
          echo "  • Original Image: ${{ needs.validate-staging-deployment.outputs.docker-image }}:${{ needs.validate-staging-deployment.outputs.docker-tag }}"
          echo "  • Deployed Image: ${{ needs.validate-staging-deployment.outputs.docker-image }}:${{ steps.prepare.outputs.staging-tag }}"
          echo "  • Application Type: ${{ needs.validate-staging-deployment.outputs.application-type }}"
          echo "  • Container Port: ${{ needs.validate-staging-deployment.outputs.container-port }}"
          echo "  • Trigger: Manual workflow dispatch"
          echo "  • Deployed by: ${{ github.actor }}"
          echo ""
          echo "🔗 Next Steps:"
          echo "  • Monitor application in ArgoCD dashboard"
          echo "  • Verify application health in STAGING environment"
          echo "  • Run integration tests"
          echo "  • Consider promotion to production if tests pass"
          echo ""
          echo "📋 Deployment Status:"
          echo "  • Docker image promotion: ✅ Complete"
          echo "  • Manifest generation: ✅ Complete"
          echo "  • ArgoCD deployment: ✅ Complete"

      - name: ❌ Staging Deployment Failed
        if: failure()
        run: |
          echo "❌ STAGING Environment Deployment Failed"
          echo ""
          echo "📊 Failed Deployment Details:"
          echo "  • Environment: STAGING"
          echo "  • Project: ${{ needs.validate-staging-deployment.outputs.project-id }}"
          echo "  • Source Image: ${{ needs.validate-staging-deployment.outputs.docker-image }}:${{ needs.validate-staging-deployment.outputs.docker-tag }}"
          echo "  • Target Image: ${{ needs.validate-staging-deployment.outputs.docker-image }}:${{ steps.prepare.outputs.staging-tag }}"
          echo "  . Container Port: ${{ needs.validate-staging-deployment.outputs.container-port }}"
          echo "  • Trigger: Manual workflow dispatch"
          echo "  • Requested by: ${{ github.actor }}"
          echo ""
          echo "🔍 Troubleshooting:"
          echo "  • Check workflow logs for detailed error information"
          echo "  • Verify source Docker image exists in registry"
          echo "  • Check DigitalOcean registry permissions"
          echo "  • Ensure ArgoCD cluster connectivity"
          echo "  • Verify all required secrets are configured"
          echo "  • Check manifest generation logs"
          echo ""
          echo "🛠️ Manual Recovery:"
          echo "  • Check if Docker image promotion succeeded"
          echo "  • Verify staging environment secrets are configured"
          echo "  • Manually apply ArgoCD manifests if needed:"
          echo "    kubectl apply -f deployments/${{ needs.validate-staging-deployment.outputs.project-id }}/argocd/project.yaml"
          echo "    kubectl apply -f deployments/${{ needs.validate-staging-deployment.outputs.project-id }}/overlays/staging/application.yaml"
