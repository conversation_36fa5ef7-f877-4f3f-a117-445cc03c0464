apiVersion: v1
kind: Secret
metadata:
  name: ai-spring-backend-saipriya-secrets
  namespace: ai-spring-backend-saipriya-dev
  labels:
    app: ai-spring-backend-saipriya
    component: secrets
    environment: dev
    app-type: springboot-backend
type: Opaque
data:
  # Backend Application Secrets
  # Essential Authentication Secrets
  JWT_SECRET: c3VwZXJzZWNyZXRrZXk=  # JWT secret for authentication
  # Managed DigitalOcean Database Credentials
  DB_USER: c3ByaW5nX2Rldl91c2Vy  # Managed database username
  DB_PASSWORD: QVZOU18wYll6dDBHWmRreTdyblA4S2w3  # Managed database password
  DB_HOST: cHJpdmF0ZS1kYmFhcy1kYi0xMDMyOTMyOC1kby11c2VyLTIzODE1NzQyLTAubS5kYi5vbmRpZ2l0YWxvY2Vhbi5jb20=  # Managed database host
  DB_PORT: MjUwNjA=  # Managed database port
  DB_NAME: c3ByaW5nX2Rldl9kYg==  # Managed database name
  DB_SSL_MODE: cmVxdWlyZQ==  # Managed database SSL mode
  # SMTP Credentials
  SMTP_USER: ****************************************  # SMTP username for email
  SMTP_PASS: ZnFhY3RlaGFmbXpsbHR6eg==  # SMTP password for email
  # OAuth2 Credentials
  GOOGLE_CLIENT_ID: MTA3Mzk4MTg2NDUzOC0zdWlpazcyb2hzZnIyb3Vpb3JvcjNmbTFqcWM0OTNvcy5hcHBzLmdvb2dsZXVzZXJjb250ZW50LmNvbQ==  # Google OAuth Client ID
  GOOGLE_CLIENT_SECRET: R09DU1BYLTcyRjBONEg5aGlMSVk1U3o1Z3pCczI5OEFBYlQ=  # Google OAuth Client Secret
  # Custom Secret Keys (add manually if needed)
  # CUSTOM_SECRET_KEY: UExBQ0VIT0xERVI=  # PLACEHOLDER - Update with actual base64 encoded value
