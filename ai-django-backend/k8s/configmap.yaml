apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-django-backend-config
  namespace: ai-django-backend-dev
  labels:
    app: ai-django-backend
    component: config
    environment: dev
    app-type: django-backend
data:
  # Application Configuration
  NODE_ENV: "dev"
  PORT: "8000"
  # Django Backend Configuration
  DJANGO_SETTINGS_MODULE: "ai-django-backend.settings.dev"
  DEBUG: "True"
  ALLOWED_HOSTS: "{{ALLOWED_HOSTS}}"
  SECRET_KEY: "{{DJANGO_SECRET_KEY}}"
  # Django Database Configuration (Managed DigitalOcean Database)
  DATABASE_URL: "postgresql://spring_dev_user:<EMAIL>:25060/spring_dev_db?sslmode=require"
  # Django Static Files
  STATIC_URL: "/static/"
  STATIC_ROOT: "/app/staticfiles/"
  MEDIA_URL: "/media/"
  MEDIA_ROOT: "/app/media/"
  # Django CORS Configuration
  CORS_ALLOWED_ORIGINS: "http://localhost:3000,http://localhost:3001"
  CORS_ALLOW_CREDENTIALS: "true"
  # Django Application URLs
  APP_URL: "http://ai-django-backend.dev.local"
  API_URL: "http://ai-django-backend-service:8000"
  # Django Security & JWT Configuration
  JWT_EXPIRATION: "24h"
  JWT_ALGORITHM: "HS256"
  # Django SMTP Configuration
  EMAIL_BACKEND: "django.core.mail.backends.smtp.EmailBackend"
  EMAIL_HOST: "smtp.gmail.com"
  EMAIL_PORT: "587"
  EMAIL_USE_TLS: "true"
  EMAIL_FROM: "<EMAIL>"
  # Django OAuth2 Configuration
  GOOGLE_OAUTH2_CLIENT_ID: "1073981864538-3uiik72ohsfr2ouioror3fm1jqc493os.apps.googleusercontent.com"
  GOOGLE_OAUTH2_REDIRECT_URI: "http://ai-django-backend.dev.local/auth/google/callback"
  SOCIAL_AUTH_GOOGLE_OAUTH2_SCOPE: "openid,profile,email"
