apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-django-backend
  namespace: ai-django-backend-dev
  labels:
    app: ai-django-backend
    component: django-backend
    version: v1.0.0
    environment: dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-django-backend
      app.kubernetes.io/name: ai-django-backend
      app.kubernetes.io/version: "1.0.0"
      app.kubernetes.io/managed-by: argocd
  template:
    metadata:
      labels:
        app: ai-django-backend
        app.kubernetes.io/name: ai-django-backend
        app.kubernetes.io/version: "1.0.0"
        app.kubernetes.io/managed-by: argocd
        component: django-backend
        version: v1.0.0
        environment: dev
    spec:
      # Backend Applications - Managed Database connectivity check
      initContainers:
      - name: wait-for-managed-postgres
        image: postgres:13-alpine
        command: ['sh', '-c']
        args:
        - |
          until pg_isready -h private-dbaas-db-10329328-do-user-23815742-0.m.db.ondigitalocean.com -p 25060 -U spring_dev_user; do
            echo "Waiting for managed PostgreSQL database to be ready..."
            sleep 2
          done
          echo "Managed PostgreSQL database is ready!"
        env:
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-secrets
              key: DB_PASSWORD
        - name: PGSSLMODE
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-secrets
              key: DB_SSL_MODE
      containers:
      - name: ai-django-backend
        image: registry.digitalocean.com/doks-registry/ai-django-backend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
        # Environment Variables from ConfigMap
        envFrom:
        - configMapRef:
            name: ai-django-backend-config
        # Backend Applications - Full secret environment variables
        env:
        # Django specific secret configuration
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-secrets
              key: DJANGO_SECRET_KEY
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-secrets
              key: JWT_SECRET
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-secrets
              key: DB_PASSWORD
        - name: EMAIL_HOST_USER
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-secrets
              key: SMTP_USER
        - name: EMAIL_HOST_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-secrets
              key: SMTP_PASS
        - name: GOOGLE_OAUTH2_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-secrets
              key: GOOGLE_CLIENT_SECRET
        # Health Checks - Application Type Specific
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
