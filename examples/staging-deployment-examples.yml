# Example Staging Deployment Configurations
# This file contains example configurations for deploying different application types
# to the staging environment using the enhanced deploy-staging.yml workflow.

# =============================================================================
# Spring Boot Application Example
# =============================================================================

spring_boot_example:
  name: "Spring Boot Backend Deployment"
  description: "Deploy a Spring Boot application to staging environment"
  workflow_inputs:
    project_id: "my-spring-backend"
    docker_image: "registry.digitalocean.com/my-registry/spring-backend"
    docker_tag: "v1.2.3"
    container_port: "8080"
    application_type: "springboot-backend"
    skip_approval: false
  
  required_secrets:
    # Core Infrastructure
    - GITOPS_TOKEN
    - DIGITALOCEAN_ACCESS_TOKEN
    
    # Spring Boot Specific
    - JWT_SECRET_SPRING
    - DB_HOST_SPRING_STAGING
    - DB_USER_SPRING_STAGING
    - DB_PASSWORD_SPRING_STAGING
    - DB_NAME_SPRING_STAGING
    
    # Optional Spring Boot Secrets
    - ENABLE_DATABASE_SPRING
    - DB_PORT_SPRING
    - DB_SSL_MODE_SPRING
    - SMTP_USER_SPRING
    - SMTP_PASS_SPRING
    - GOOGLE_CLIENT_ID_SPRING
    - GOOGLE_CLIENT_SECRET_SPRING

  expected_behavior:
    - Validates Spring Boot specific secrets
    - Creates staging-tagged Docker image
    - Generates Spring Boot Kubernetes manifests
    - Deploys to staging namespace with Spring Boot configuration
    - Configures database connection for Spring Boot
    - Sets up JWT authentication for Spring Boot

# =============================================================================
# NestJS Application Example
# =============================================================================

nestjs_example:
  name: "NestJS Backend Deployment"
  description: "Deploy a NestJS application to staging environment"
  workflow_inputs:
    project_id: "my-nest-api"
    docker_image: "registry.digitalocean.com/my-registry/nest-api"
    docker_tag: "v2.1.0"
    container_port: "3000"
    application_type: "nest-backend"
    skip_approval: false
  
  required_secrets:
    # Core Infrastructure
    - GITOPS_TOKEN
    - DIGITALOCEAN_ACCESS_TOKEN
    
    # NestJS Specific
    - JWT_SECRET_NEST
    - SESSION_SECRET_NEST
    - DB_HOST_NEST_STAGING
    - DB_USER_NEST_STAGING
    - DB_PASSWORD_NEST_STAGING
    - DB_NAME_NEST_STAGING
    
    # Optional NestJS Secrets
    - ENABLE_DATABASE_NEST
    - DB_PORT_NEST
    - DB_SSL_MODE_NEST
    - SMTP_USER_NEST
    - SMTP_PASS_NEST
    - GOOGLE_CLIENT_ID_NEST
    - GOOGLE_CLIENT_SECRET_NEST

  expected_behavior:
    - Validates NestJS specific secrets
    - Creates staging-tagged Docker image
    - Generates NestJS Kubernetes manifests
    - Deploys to staging namespace with NestJS configuration
    - Configures database connection for NestJS
    - Sets up JWT and session authentication for NestJS

# =============================================================================
# Django Application Example
# =============================================================================

django_example:
  name: "Django Backend Deployment"
  description: "Deploy a Django application to staging environment"
  workflow_inputs:
    project_id: "my-django-app"
    docker_image: "registry.digitalocean.com/my-registry/django-app"
    docker_tag: "v3.0.1"
    container_port: "8000"
    application_type: "django-backend"
    skip_approval: false
  
  required_secrets:
    # Core Infrastructure
    - GITOPS_TOKEN
    - DIGITALOCEAN_ACCESS_TOKEN
    
    # Django Specific
    - JWT_SECRET_DJANGO
    - DJANGO_SECRET_KEY
    - SESSION_SECRET_DJANGO
    - DB_HOST_DJANGO_STAGING
    - DB_USER_DJANGO_STAGING
    - DB_PASSWORD_DJANGO_STAGING
    - DB_NAME_DJANGO_STAGING
    
    # Optional Django Secrets
    - ENABLE_DATABASE_DJANGO
    - DB_PORT_DJANGO
    - DB_SSL_MODE_DJANGO
    - SMTP_USER_DJANGO
    - SMTP_PASS_DJANGO
    - GOOGLE_CLIENT_ID_DJANGO
    - GOOGLE_CLIENT_SECRET_DJANGO
    - RATE_LIMIT_WINDOW_MS_DJANGO
    - RATE_LIMIT_MAX_REQUESTS_DJANGO
    - PASSWORD_RESET_TOKEN_EXPIRY_DJANGO
    - EMAIL_VERIFICATION_TOKEN_EXPIRY_DJANGO

  expected_behavior:
    - Validates Django specific secrets
    - Creates staging-tagged Docker image
    - Generates Django Kubernetes manifests
    - Deploys to staging namespace with Django configuration
    - Configures database connection for Django
    - Sets up JWT, session, and Django-specific authentication

# =============================================================================
# Testing Scenarios
# =============================================================================

testing_scenarios:
  
  # Test with skip approval
  quick_test:
    description: "Quick deployment test with approval skip"
    workflow_inputs:
      project_id: "test-spring-app"
      docker_image: "registry.digitalocean.com/my-registry/test-app"
      docker_tag: "test-v1.0.0"
      container_port: "8080"
      application_type: "springboot-backend"
      skip_approval: true
    notes:
      - "Use this for testing the deployment pipeline"
      - "Skips the approval gate for faster iteration"
      - "Ideal for development and testing phases"

  # Test with different port
  custom_port_test:
    description: "Test deployment with custom port configuration"
    workflow_inputs:
      project_id: "custom-nest-app"
      docker_image: "registry.digitalocean.com/my-registry/custom-app"
      docker_tag: "v1.0.0"
      container_port: "4000"  # Custom port instead of default 3000
      application_type: "nest-backend"
      skip_approval: false
    notes:
      - "Tests custom port configuration"
      - "Ensures port mapping works correctly"
      - "Validates service configuration"

# =============================================================================
# Common Troubleshooting Scenarios
# =============================================================================

troubleshooting:
  
  missing_secrets:
    problem: "Deployment fails with missing secrets error"
    solution:
      - "Run the validation script: ./scripts/validate-app-secrets.sh"
      - "Add missing secrets to GitHub repository secrets"
      - "Ensure secret names match exactly (case-sensitive)"
    
  wrong_application_type:
    problem: "Application type not recognized"
    solution:
      - "Use one of: springboot-backend, nest-backend, django-backend"
      - "Check spelling and case sensitivity"
      - "Refer to the application type dropdown in workflow"
    
  docker_image_not_found:
    problem: "Docker image pull fails"
    solution:
      - "Verify image exists in DigitalOcean Container Registry"
      - "Check image name and tag are correct"
      - "Ensure DIGITALOCEAN_ACCESS_TOKEN has registry access"
    
  database_connection_failed:
    problem: "Application cannot connect to database"
    solution:
      - "Verify staging database secrets are correct"
      - "Check database host, user, password, and name"
      - "Ensure database instance is running and accessible"

# =============================================================================
# Best Practices
# =============================================================================

best_practices:
  
  secret_management:
    - "Use different database instances for each application type"
    - "Rotate secrets regularly for security"
    - "Use descriptive but secure secret values"
    - "Never commit secrets to code repositories"
  
  deployment_strategy:
    - "Test with skip_approval=true first"
    - "Use semantic versioning for Docker tags"
    - "Deploy to staging before production"
    - "Monitor application health after deployment"
  
  naming_conventions:
    - "Use consistent project_id naming (lowercase, hyphens)"
    - "Include application type in project names when helpful"
    - "Use environment-specific Docker tags"
    - "Follow organizational naming standards"
