apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-nest-backend
  namespace: ai-nest-backend-dev
  labels:
    app: ai-nest-backend
    component: nest-backend
    version: v1.0.0
    environment: dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-nest-backend
      app.kubernetes.io/name: ai-nest-backend
      app.kubernetes.io/version: "1.0.0"
      app.kubernetes.io/managed-by: argocd
  template:
    metadata:
      labels:
        app: ai-nest-backend
        app.kubernetes.io/name: ai-nest-backend
        app.kubernetes.io/version: "1.0.0"
        app.kubernetes.io/managed-by: argocd
        component: nest-backend
        version: v1.0.0
        environment: dev
    spec:
      # Backend Applications - Managed Database connectivity check
      initContainers:
      - name: wait-for-managed-postgres
        image: postgres:13-alpine
        command: ['sh', '-c']
        args:
        - |
          until pg_isready -h private-dbaas-db-10329328-do-user-23815742-0.m.db.ondigitalocean.com -p 25060 -U nest_dev_user; do
            echo "Waiting for managed PostgreSQL database to be ready..."
            sleep 2
          done
          echo "Managed PostgreSQL database is ready!"
        env:
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: DB_PASSWORD
        - name: PGSSLMODE
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: DB_SSL_MODE
      containers:
      - name: ai-nest-backend
        image: registry.digitalocean.com/doks-registry/ai-nest-backend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
          name: http
        # Environment Variables from ConfigMap
        envFrom:
        - configMapRef:
            name: ai-nest-backend-config
        # Backend Applications - Full secret environment variables
        env:
        # NestJS specific secret configuration
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: JWT_SECRET
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: DB_PASSWORD
        - name: SMTP_USER
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: SMTP_USER
        - name: SMTP_PASS
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: SMTP_PASS
        - name: GOOGLE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: GOOGLE_CLIENT_SECRET
        # Health Checks - Application Type Specific
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
