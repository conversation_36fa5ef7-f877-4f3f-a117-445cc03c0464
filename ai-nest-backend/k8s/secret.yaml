apiVersion: v1
kind: Secret
metadata:
  name: ai-nest-backend-secrets
  namespace: ai-nest-backend-dev
  labels:
    app: ai-nest-backend
    component: secrets
    environment: dev
    app-type: nest-backend
type: Opaque
data:
  # Backend Application Secrets
  # Essential Authentication Secrets
  JWT_SECRET: c3VwZXJzZWNyZXRrZXk=  # User-provided JWT secret
  # Managed DigitalOcean Database Credentials
  DB_USER: bmVzdF9kZXZfdXNlcg==  # Managed database username
  DB_PASSWORD: QVZOU19xczczanFuY3FkZ01ZdFpCemIz  # Managed database password
  DB_HOST: cHJpdmF0ZS1kYmFhcy1kYi0xMDMyOTMyOC1kby11c2VyLTIzODE1NzQyLTAubS5kYi5vbmRpZ2l0YWxvY2Vhbi5jb20=  # Managed database host
  DB_PORT: MjUwNjA=  # Managed database port
  DB_NAME: bmVzdF9kZXZfZGI=  # Managed database name
  DB_SSL_MODE: cmVxdWlyZQ==  # Managed database SSL mode
  # SMTP Credentials
  SMTP_USER: ****************************************  # User-provided SMTP username
  SMTP_PASS: ZnFhY3RlaGFmbXpsbHR6eg==  # User-provided SMTP password
  # OAuth2 Credentials
  GOOGLE_CLIENT_ID: MTA3Mzk4MTg2NDUzOC0zdWlpazcyb2hzZnIyb3Vpb3JvcjNmbTFqcWM0OTNvcy5hcHBzLmdvb2dsZXVzZXJjb250ZW50LmNvbQ==  # User-provided Google Client ID
  GOOGLE_CLIENT_SECRET: R09DU1BYLTcyRjBONEg5aGlMSVk1U3o1Z3pCczI5OEFBYlQ=  # User-provided Google Client Secret
  # Custom Secret Keys (add manually if needed)
  # CUSTOM_SECRET_KEY: UExBQ0VIT0xERVI=  # PLACEHOLDER - Update with actual base64 encoded value
